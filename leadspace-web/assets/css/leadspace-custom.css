/* LeadSpace Custom Styles */
:root {
    --leadspace-red: #D72828;
    --leadspace-black: #14140E;
    --leadspace-white: #FFFFFF;
    --leadspace-light-gray: #F8F9FA;
    --leadspace-gray: #6C757D;
    --leadspace-dark-gray: #343A40;
}

/* Global Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--leadspace-black);
    line-height: 1.6;
}

.text-primary {
    color: var(--leadspace-red) !important;
}

.bg-primary {
    background-color: var(--leadspace-red) !important;
}

/* Buttons */
.leadspace-btn-primary {
    background-color: var(--leadspace-red);
    border: 2px solid var(--leadspace-red);
    color: white;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.leadspace-btn-primary:hover {
    background-color: transparent;
    color: var(--leadspace-red);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(215, 40, 40, 0.2);
}

.leadspace-btn-outline {
    background-color: transparent;
    border: 2px solid var(--leadspace-black);
    color: var(--leadspace-black);
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.leadspace-btn-outline:hover {
    background-color: var(--leadspace-black);
    color: white;
    transform: translateY(-2px);
}

/* Header */
.leadspace-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    transition: all 0.3s ease;
}

.leadspace-logo a {
    text-decoration: none;
}

.navbar-nav .nav-link {
    color: var(--leadspace-black);
    font-weight: 500;
    margin: 0 15px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--leadspace-red);
}

/* Hero Section */
.leadspace-hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.leadspace-hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23f0f0f0" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.leadspace-hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--leadspace-black);
}

.leadspace-hero-subtitle {
    font-size: 1.25rem;
    color: var(--leadspace-gray);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-stats {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.hero-image-grid {
    position: relative;
    z-index: 2;
}

.hero-media-card {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.hero-media-card:hover {
    transform: translateY(-5px);
}

.media-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 20px;
    text-align: center;
}

/* Sections */
.leadspace-section {
    padding: 80px 0;
}

.section-header {
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--leadspace-black);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--leadspace-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* Media Type Cards */
.media-type-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    height: 100%;
}

.media-type-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: var(--leadspace-red);
}

.media-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--leadspace-red), #ff4757);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.media-type-card h4 {
    color: var(--leadspace-black);
    margin-bottom: 15px;
    font-weight: 600;
}

.media-features {
    list-style: none;
    padding: 0;
    margin-top: 20px;
}

.media-features li {
    padding: 5px 0;
    color: var(--leadspace-gray);
    position: relative;
    padding-left: 20px;
}

.media-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--leadspace-red);
    font-weight: bold;
}

/* Process Steps */
.process-step {
    text-align: center;
    position: relative;
    padding: 30px 20px;
}

.step-number {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: var(--leadspace-red);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.step-icon {
    width: 100px;
    height: 100px;
    background: var(--leadspace-light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2.5rem;
    color: var(--leadspace-red);
    transition: all 0.3s ease;
}

.process-step:hover .step-icon {
    background: var(--leadspace-red);
    color: white;
    transform: scale(1.1);
}

/* Form Styles */
.requirement-form {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.leadspace-form .form-control,
.leadspace-form .form-select {
    border: 2px solid #f0f0f0;
    border-radius: 10px;
    padding: 15px 20px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.leadspace-form .form-control:focus,
.leadspace-form .form-select:focus {
    border-color: var(--leadspace-red);
    box-shadow: 0 0 0 0.2rem rgba(215, 40, 40, 0.25);
}

.benefits-list {
    margin-top: 30px;
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 500;
}

.benefit-item i {
    margin-right: 15px;
    font-size: 1.2rem;
}

/* Testimonials */
.testimonial-card {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: 100%;
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
}

.testimonial-author {
    display: flex;
    align-items: center;
    margin-top: 20px;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
}

.author-info h5 {
    margin: 0;
    font-weight: 600;
    color: var(--leadspace-black);
}

.author-info span {
    color: var(--leadspace-gray);
    font-size: 0.9rem;
}

/* Brand Slider */
.brands-slider {
    overflow: hidden;
}

.brand-logo {
    max-height: 60px;
    opacity: 0.6;
    transition: opacity 0.3s ease;
    filter: grayscale(100%);
}

.brand-logo:hover {
    opacity: 1;
    filter: grayscale(0%);
}

/* Footer */
.leadspace-footer {
    background: var(--leadspace-black);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    margin-bottom: 40px;
}

.footer-brand h3 {
    color: var(--leadspace-red);
}

.footer-links h5 {
    color: white;
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links ul li a:hover {
    color: var(--leadspace-red);
}

.social-links {
    margin-top: 20px;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--leadspace-red);
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-3px);
}

.contact-info {
    margin-top: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.contact-item i {
    color: var(--leadspace-red);
    margin-right: 15px;
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 20px;
    color: #ccc;
}

.footer-link {
    color: #ccc;
    text-decoration: none;
    margin-left: 20px;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--leadspace-red);
}

/* Responsive Design */
@media (max-width: 768px) {
    .leadspace-hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-buttons {
        text-align: center;
    }
    
    .hero-buttons .btn {
        display: block;
        margin: 10px 0;
        width: 100%;
    }
    
    .requirement-form {
        padding: 30px 20px;
    }
    
    .leadspace-section {
        padding: 60px 0;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
.preloader {
    background: white;
}

.leadspace-loader-logo {
    margin: 20px 0;
}
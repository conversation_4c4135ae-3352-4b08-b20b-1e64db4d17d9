@font-face {
  font-family: 'icomoon';
  src: url('../fonts/icomoon.ttf');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}



.isax {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-spcial-arrow-left:before {
  content: "\f01b";
}
.icon-spcial-arrow-right:before {
  content: "\f01c";
}
.icon-sun-fog:before {
  content: "\e900";
}
.icon-sun:before {
  content: "\e901";
}
.icon-tag-2:before {
  content: "\e902";
}
.icon-tag-cross:before {
  content: "\e903";
}
.icon-tag-right:before {
  content: "\e904";
}
.icon-tag-user:before {
  content: "\e905";
}
.icon-tag:before {
  content: "\e906";
}
.icon-task-square:before {
  content: "\e907";
}
.icon-task:before {
  content: "\e908";
}
.icon-teacher:before {
  content: "\e909";
}
.icon-text-block:before {
  content: "\e90a";
}
.icon-triangle:before {
  content: "\e90b";
}
.icon-truck-fast:before {
  content: "\e90c";
}
.icon-truck-remove:before {
  content: "\e90d";
}
.icon-truck-tick:before {
  content: "\e90e";
}
.icon-truck-time:before {
  content: "\e90f";
}
.icon-truck:before {
  content: "\e910";
}
.icon-trush-square:before {
  content: "\e911";
}
.icon-undo:before {
  content: "\e912";
}
.icon-unlimited:before {
  content: "\e913";
}
.icon-unlock:before {
  content: "\e914";
}
.icon-user-add:before {
  content: "\e915";
}
.icon-user-cirlce-add:before {
  content: "\e916";
}
.icon-user-edit:before {
  content: "\e917";
}
.icon-user-minus:before {
  content: "\e918";
}
.icon-user-octagon:before {
  content: "\e919";
}
.icon-user-remove:before {
  content: "\e91a";
}
.icon-user-search:before {
  content: "\e91b";
}
.icon-user-square:before {
  content: "\e91c";
}
.icon-user-tag:before {
  content: "\e91d";
}
.icon-user-tick:before {
  content: "\e91e";
}
.icon-user:before {
  content: "\e91f";
}
.icon-verify:before {
  content: "\e920";
}
.icon-video-add:before {
  content: "\e921";
}
.icon-video-circle:before {
  content: "\e922";
}
.icon-video-horizontal:before {
  content: "\e923";
}
.icon-video-octagon:before {
  content: "\e924";
}
.icon-video-play:before {
  content: "\e925";
}
.icon-video-remove:before {
  content: "\e926";
}
.icon-video-slash:before {
  content: "\e927";
}
.icon-video-square:before {
  content: "\e928";
}
.icon-video-tick:before {
  content: "\e929";
}
.icon-video-time:before {
  content: "\e92a";
}
.icon-video-vertical:before {
  content: "\e92b";
}
.icon-video:before {
  content: "\e92c";
}
.icon-voice-cricle:before {
  content: "\e92d";
}
.icon-voice-square:before {
  content: "\e92e";
}
.icon-volume-cross:before {
  content: "\e92f";
}
.icon-volume-high:before {
  content: "\e930";
}
.icon-volume-low-1:before {
  content: "\e931";
}
.icon-volume-low:before {
  content: "\e932";
}
.icon-volume-mute:before {
  content: "\e933";
}
.icon-volume-slash:before {
  content: "\e934";
}
.icon-volume-up:before {
  content: "\e935";
}
.icon-wallet-1:before {
  content: "\e936";
}
.icon-wallet-2:before {
  content: "\e937";
}
.icon-wallet-3:before {
  content: "\e938";
}
.icon-wallet-add-1:before {
  content: "\e939";
}
.icon-wallet-add:before {
  content: "\e93a";
}
.icon-wallet-check:before {
  content: "\e93b";
}
.icon-wallet-minus:before {
  content: "\e93c";
}
.icon-wallet-money:before {
  content: "\e93d";
}
.icon-wallet-remove:before {
  content: "\e93e";
}
.icon-wallet-search:before {
  content: "\e93f";
}
.icon-wallet:before {
  content: "\e940";
}
.icon-warning-2:before {
  content: "\e941";
}
.icon-watch-status:before {
  content: "\e942";
}
.icon-watch:before {
  content: "\e943";
}
.icon-weight-1:before {
  content: "\e944";
}
.icon-weight:before {
  content: "\e945";
}
.icon-wifi-square:before {
  content: "\e946";
}
.icon-wifi:before {
  content: "\e947";
}
.icon-wind-2:before {
  content: "\e948";
}
.icon-wind:before {
  content: "\e949";
}
.icon-woman:before {
  content: "\e94a";
}
.icon-repeate-music:before {
  content: "\e94b";
}
.icon-repeate-one:before {
  content: "\e94c";
}
.icon-reserve:before {
  content: "\e94d";
}
.icon-rotate-left-1:before {
  content: "\e94e";
}
.icon-rotate-left:before {
  content: "\e94f";
}
.icon-rotate-right-1:before {
  content: "\e950";
}
.icon-rotate-right:before {
  content: "\e951";
}
.icon-route-square:before {
  content: "\e952";
}
.icon-routing-2:before {
  content: "\e953";
}
.icon-routing:before {
  content: "\e954";
}
.icon-row-horizontal:before {
  content: "\e955";
}
.icon-row-vertical:before {
  content: "\e956";
}
.icon-ruler:before {
  content: "\e957";
}
.icon-rulerpen:before {
  content: "\e958";
}
.icon-safe-home:before {
  content: "\e959";
}
.icon-sagittarius:before {
  content: "\e95a";
}
.icon-save-2:before {
  content: "\e95b";
}
.icon-save-add:before {
  content: "\e95c";
}
.icon-save-minus:before {
  content: "\e95d";
}
.icon-save-remove:before {
  content: "\e95e";
}
.icon-scan-barcode:before {
  content: "\e95f";
}
.icon-scan:before {
  content: "\e960";
}
.icon-scanner:before {
  content: "\e961";
}
.icon-scanning:before {
  content: "\e962";
}
.icon-scissor-1:before {
  content: "\e963";
}
.icon-scissor:before {
  content: "\e964";
}
.icon-screenmirroring:before {
  content: "\e965";
}
.icon-scroll:before {
  content: "\e966";
}
.icon-search-favorite-1:before {
  content: "\e967";
}
.icon-search-favorite:before {
  content: "\e968";
}
.icon-search-normal-1:before {
  content: "\e969";
}
.icon-search-normal:before {
  content: "\e96a";
}
.icon-search-status-1:before {
  content: "\e96b";
}
.icon-search-status:before {
  content: "\e96c";
}
.icon-search-zoom-in-1:before {
  content: "\e96d";
}
.icon-search-zoom-in:before {
  content: "\e96e";
}
.icon-search-zoom-out-1:before {
  content: "\e96f";
}
.icon-search-zoom-out:before {
  content: "\e970";
}
.icon-security-card:before {
  content: "\e971";
}
.icon-security-safe:before {
  content: "\e972";
}
.icon-security-time:before {
  content: "\e973";
}
.icon-security-user:before {
  content: "\e974";
}
.icon-security:before {
  content: "\e975";
}
.icon-send-1:before {
  content: "\e976";
}
.icon-send-2:before {
  content: "\e977";
}
.icon-send-sqaure-2:before {
  content: "\e978";
}
.icon-send-square:before {
  content: "\e979";
}
.icon-send:before {
  content: "\e97a";
}
.icon-setting-2:before {
  content: "\e97b";
}
.icon-setting-3:before {
  content: "\e97c";
}
.icon-setting-4:before {
  content: "\e97d";
}
.icon-setting-5:before {
  content: "\e97e";
}
.icon-setting:before {
  content: "\e97f";
}
.icon-settings:before {
  content: "\e980";
}
.icon-shapes-1:before {
  content: "\e981";
}
.icon-shapes:before {
  content: "\e982";
}
.icon-share:before {
  content: "\e983";
}
.icon-shield-cross:before {
  content: "\e984";
}
.icon-shield-search:before {
  content: "\e985";
}
.icon-shield-security:before {
  content: "\e986";
}
.icon-shield-slash:before {
  content: "\e987";
}
.icon-shield-tick:before {
  content: "\e988";
}
.icon-ship:before {
  content: "\e989";
}
.icon-shop-add:before {
  content: "\e98a";
}
.icon-shop-remove:before {
  content: "\e98b";
}
.icon-shop:before {
  content: "\e98c";
}
.icon-shopping-bag:before {
  content: "\e98d";
}
.icon-shopping-cart:before {
  content: "\e98e";
}
.icon-shuffle:before {
  content: "\e98f";
}
.icon-sidebar-bottom:before {
  content: "\e990";
}
.icon-sidebar-left:before {
  content: "\e991";
}
.icon-sidebar-right:before {
  content: "\e992";
}
.icon-sidebar-top:before {
  content: "\e993";
}
.icon-signpost:before {
  content: "\e994";
}
.icon-simcard-1:before {
  content: "\e995";
}
.icon-simcard-2:before {
  content: "\e996";
}
.icon-simcard:before {
  content: "\e997";
}
.icon-size:before {
  content: "\e998";
}
.icon-slash:before {
  content: "\e999";
}
.icon-slider-horizontal-1:before {
  content: "\e99a";
}
.icon-slider-horizontal:before {
  content: "\e99b";
}
.icon-slider-vertical-1:before {
  content: "\e99c";
}
.icon-slider-vertical:before {
  content: "\e99d";
}
.icon-slider:before {
  content: "\e99e";
}
.icon-smallcaps:before {
  content: "\e99f";
}
.icon-smart-car:before {
  content: "\e9a0";
}
.icon-smart-home:before {
  content: "\e9a1";
}
.icon-smileys:before {
  content: "\e9a2";
}
.icon-sms-edit:before {
  content: "\e9a3";
}
.icon-sms-notification:before {
  content: "\e9a4";
}
.icon-sms-search:before {
  content: "\e9a5";
}
.icon-sms-star:before {
  content: "\e9a6";
}
.icon-sms-tracking:before {
  content: "\e9a7";
}
.icon-sms:before {
  content: "\e9a8";
}
.icon-sort:before {
  content: "\e9a9";
}
.icon-sound:before {
  content: "\e9aa";
}
.icon-speaker:before {
  content: "\e9ab";
}
.icon-speedometer:before {
  content: "\e9ac";
}
.icon-star-1:before {
  content: "\e9ad";
}
.icon-star-slash:before {
  content: "\e9ae";
}
.icon-star:before {
  content: "\e9af";
}
.icon-status-up:before {
  content: "\e9b0";
}
.icon-status:before {
  content: "\e9b1";
}
.icon-sticker:before {
  content: "\e9b2";
}
.icon-stickynote:before {
  content: "\e9b3";
}
.icon-stop-circle:before {
  content: "\e9b4";
}
.icon-stop:before {
  content: "\e9b5";
}
.icon-story:before {
  content: "\e9b6";
}
.icon-strongbox-2:before {
  content: "\e9b7";
}
.icon-strongbox:before {
  content: "\e9b8";
}
.icon-subtitle:before {
  content: "\e9b9";
}
.icon-sun-1:before {
  content: "\e9ba";
}
.icon-text-bold:before {
  content: "\e9bb";
}
.icon-text-italic:before {
  content: "\e9bc";
}
.icon-text-underline:before {
  content: "\e9bd";
}
.icon-text:before {
  content: "\e9be";
}
.icon-textalign-center:before {
  content: "\e9bf";
}
.icon-textalign-justifycenter:before {
  content: "\e9c0";
}
.icon-textalign-justifyleft:before {
  content: "\e9c1";
}
.icon-textalign-justifyright:before {
  content: "\e9c2";
}
.icon-textalign-left:before {
  content: "\e9c3";
}
.icon-textalign-right:before {
  content: "\e9c4";
}
.icon-tick-circle:before {
  content: "\e9c5";
}
.icon-tick-square:before {
  content: "\e9c6";
}
.icon-ticket-2:before {
  content: "\e9c7";
}
.icon-ticket-discount:before {
  content: "\e9c8";
}
.icon-ticket-expired:before {
  content: "\e9c9";
}
.icon-ticket-star:before {
  content: "\e9ca";
}
.icon-ticket:before {
  content: "\e9cb";
}
.icon-timer-1:before {
  content: "\e9cc";
}
.icon-timer-pause:before {
  content: "\e9cd";
}
.icon-timer-start:before {
  content: "\e9ce";
}
.icon-timer:before {
  content: "\e9cf";
}
.icon-toggle-off-circle:before {
  content: "\e9d0";
}
.icon-toggle-off:before {
  content: "\e9d1";
}
.icon-toggle-on-circle:before {
  content: "\e9d2";
}
.icon-toggle-on:before {
  content: "\e9d3";
}
.icon-trade:before {
  content: "\e9d4";
}
.icon-transaction-minus:before {
  content: "\e9d5";
}
.icon-translate:before {
  content: "\e9d6";
}
.icon-trash:before {
  content: "\e9d7";
}
.icon-tree:before {
  content: "\e9d8";
}
.icon-trend-down:before {
  content: "\e9d9";
}
.icon-trend-up:before {
  content: "\e9da";
}
.icon-message-add:before {
  content: "\e9db";
}
.icon-message-circle:before {
  content: "\e9dc";
}
.icon-message-edit:before {
  content: "\e9dd";
}
.icon-message-favorite:before {
  content: "\e9de";
}
.icon-message-minus:before {
  content: "\e9df";
}
.icon-message-notif:before {
  content: "\e9e0";
}
.icon-message-programming:before {
  content: "\e9e1";
}
.icon-message-question:before {
  content: "\e9e2";
}
.icon-message-remove:before {
  content: "\e9e3";
}
.icon-message-search:before {
  content: "\e9e4";
}
.icon-message-square:before {
  content: "\e9e5";
}
.icon-message-text-1:before {
  content: "\e9e6";
}
.icon-message-text:before {
  content: "\e9e7";
}
.icon-message-tick:before {
  content: "\e9e8";
}
.icon-message-time:before {
  content: "\e9e9";
}
.icon-message:before {
  content: "\e9ea";
}
.icon-messages-1:before {
  content: "\e9eb";
}
.icon-messages-2:before {
  content: "\e9ec";
}
.icon-messages-3:before {
  content: "\e9ed";
}
.icon-messages:before {
  content: "\e9ee";
}
.icon-microphone-2:before {
  content: "\e9ef";
}
.icon-microphone-slash-1:before {
  content: "\e9f0";
}
.icon-microphone-slash:before {
  content: "\e9f1";
}
.icon-microphone:before {
  content: "\e9f2";
}
.icon-microscope:before {
  content: "\e9f3";
}
.icon-milk:before {
  content: "\e9f4";
}
.icon-mini-music-sqaure:before {
  content: "\e9f5";
}
.icon-minus-cirlce:before {
  content: "\e9f6";
}
.icon-minus-square:before {
  content: "\e9f7";
}
.icon-minus:before {
  content: "\e9f8";
}
.icon-mirror:before {
  content: "\e9f9";
}
.icon-mirroring-screen:before {
  content: "\e9fa";
}
.icon-mobile-programming:before {
  content: "\e9fb";
}
.icon-mobile:before {
  content: "\e9fc";
}
.icon-money-2:before {
  content: "\e9fd";
}
.icon-money-3:before {
  content: "\e9fe";
}
.icon-money-4:before {
  content: "\e9ff";
}
.icon-money-add:before {
  content: "\ea00";
}
.icon-money-change:before {
  content: "\ea01";
}
.icon-money-forbidden:before {
  content: "\ea02";
}
.icon-money-recive:before {
  content: "\ea03";
}
.icon-money-remove:before {
  content: "\ea04";
}
.icon-money-send:before {
  content: "\ea05";
}
.icon-money-tick:before {
  content: "\ea06";
}
.icon-money-time:before {
  content: "\ea07";
}
.icon-money:before {
  content: "\ea08";
}
.icon-moneys:before {
  content: "\ea09";
}
.icon-monitor-mobbile:before {
  content: "\ea0a";
}
.icon-monitor-recorder:before {
  content: "\ea0b";
}
.icon-monitor:before {
  content: "\ea0c";
}
.icon-moon:before {
  content: "\ea0d";
}
.icon-more-2:before {
  content: "\ea0e";
}
.icon-more-circle:before {
  content: "\ea0f";
}
.icon-more-square:before {
  content: "\ea10";
}
.icon-more:before {
  content: "\ea11";
}
.icon-mouse-1:before {
  content: "\ea12";
}
.icon-mouse-circle:before {
  content: "\ea13";
}
.icon-mouse-square:before {
  content: "\ea14";
}
.icon-mouse:before {
  content: "\ea15";
}
.icon-music-circle:before {
  content: "\ea16";
}
.icon-music-dashboard:before {
  content: "\ea17";
}
.icon-music-filter:before {
  content: "\ea18";
}
.icon-music-library-2:before {
  content: "\ea19";
}
.icon-music-play:before {
  content: "\ea1a";
}
.icon-music-playlist:before {
  content: "\ea1b";
}
.icon-music-square-add:before {
  content: "\ea1c";
}
.icon-music-square-remove:before {
  content: "\ea1d";
}
.icon-music-square-search:before {
  content: "\ea1e";
}
.icon-music-square:before {
  content: "\ea1f";
}
.icon-music:before {
  content: "\ea20";
}
.icon-musicnote:before {
  content: "\ea21";
}
.icon-next:before {
  content: "\ea22";
}
.icon-note-1:before {
  content: "\ea23";
}
.icon-note-2:before {
  content: "\ea24";
}
.icon-note-21:before {
  content: "\ea25";
}
.icon-note-add:before {
  content: "\ea26";
}
.icon-note-favorite:before {
  content: "\ea27";
}
.icon-note-remove:before {
  content: "\ea28";
}
.icon-note-square:before {
  content: "\ea29";
}
.icon-note-text:before {
  content: "\ea2a";
}
.icon-note:before {
  content: "\ea2b";
}
.icon-notification-1:before {
  content: "\ea2c";
}
.icon-notification-bing:before {
  content: "\ea2d";
}
.icon-notification-favorite:before {
  content: "\ea2e";
}
.icon-notification-status:before {
  content: "\ea2f";
}
.icon-notification:before {
  content: "\ea30";
}
.icon-omega-circle:before {
  content: "\ea31";
}
.icon-omega-square:before {
  content: "\ea32";
}
.icon-paintbucket:before {
  content: "\ea33";
}
.icon-paperclip-2:before {
  content: "\ea34";
}
.icon-paperclip:before {
  content: "\ea35";
}
.icon-password-check:before {
  content: "\ea36";
}
.icon-path-2:before {
  content: "\ea37";
}
.icon-path-square:before {
  content: "\ea38";
}
.icon-path:before {
  content: "\ea39";
}
.icon-pause-circle:before {
  content: "\ea3a";
}
.icon-pause:before {
  content: "\ea3b";
}
.icon-pen-add:before {
  content: "\ea3c";
}
.icon-pen-close:before {
  content: "\ea3d";
}
.icon-pen-remove:before {
  content: "\ea3e";
}
.icon-pen-tool-2:before {
  content: "\ea3f";
}
.icon-pen-tool:before {
  content: "\ea40";
}
.icon-people:before {
  content: "\ea41";
}
.icon-percentage-circle:before {
  content: "\ea42";
}
.icon-percentage-square:before {
  content: "\ea43";
}
.icon-personalcard:before {
  content: "\ea44";
}
.icon-pet:before {
  content: "\ea45";
}
.icon-pharagraphspacing:before {
  content: "\ea46";
}
.icon-picture-frame:before {
  content: "\ea47";
}
.icon-play-add:before {
  content: "\ea48";
}
.icon-play-circle:before {
  content: "\ea49";
}
.icon-play-cricle:before {
  content: "\ea4a";
}
.icon-play-remove:before {
  content: "\ea4b";
}
.icon-play:before {
  content: "\ea4c";
}
.icon-presention-chart:before {
  content: "\ea4d";
}
.icon-previous:before {
  content: "\ea4e";
}
.icon-printer-slash:before {
  content: "\ea4f";
}
.icon-printer:before {
  content: "\ea50";
}
.icon-profile-2user:before {
  content: "\ea51";
}
.icon-profile-add:before {
  content: "\ea52";
}
.icon-profile-circle:before {
  content: "\ea53";
}
.icon-profile-delete:before {
  content: "\ea54";
}
.icon-profile-remove:before {
  content: "\ea55";
}
.icon-profile-tick:before {
  content: "\ea56";
}
.icon-programming-arrow:before {
  content: "\ea57";
}
.icon-programming-arrows:before {
  content: "\ea58";
}
.icon-quote-down-circle:before {
  content: "\ea59";
}
.icon-quote-down-square:before {
  content: "\ea5a";
}
.icon-quote-down:before {
  content: "\ea5b";
}
.icon-quote-up-circle:before {
  content: "\ea5c";
}
.icon-quote-up-square:before {
  content: "\ea5d";
}
.icon-quote-up:before {
  content: "\ea5e";
}
.icon-radar-1:before {
  content: "\ea5f";
}
.icon-radar-2:before {
  content: "\ea60";
}
.icon-radar:before {
  content: "\ea61";
}
.icon-radio:before {
  content: "\ea62";
}
.icon-ram-2:before {
  content: "\ea63";
}
.icon-ram:before {
  content: "\ea64";
}
.icon-ranking-1:before {
  content: "\ea65";
}
.icon-ranking:before {
  content: "\ea66";
}
.icon-receipt-1:before {
  content: "\ea67";
}
.icon-receipt-2-1:before {
  content: "\ea68";
}
.icon-receipt-2:before {
  content: "\ea69";
}
.icon-receipt-add:before {
  content: "\ea6a";
}
.icon-receipt-discount:before {
  content: "\ea6b";
}
.icon-receipt-disscount:before {
  content: "\ea6c";
}
.icon-receipt-edit:before {
  content: "\ea6d";
}
.icon-receipt-item:before {
  content: "\ea6e";
}
.icon-receipt-minus:before {
  content: "\ea6f";
}
.icon-receipt-search:before {
  content: "\ea70";
}
.icon-receipt-square:before {
  content: "\ea71";
}
.icon-receipt-text:before {
  content: "\ea72";
}
.icon-receipt:before {
  content: "\ea73";
}
.icon-receive-square-2:before {
  content: "\ea74";
}
.icon-receive-square:before {
  content: "\ea75";
}
.icon-received:before {
  content: "\ea76";
}
.icon-record-circle:before {
  content: "\ea77";
}
.icon-record:before {
  content: "\ea78";
}
.icon-recovery-convert:before {
  content: "\ea79";
}
.icon-redo:before {
  content: "\ea7a";
}
.icon-refresh-2:before {
  content: "\ea7b";
}
.icon-refresh-circle:before {
  content: "\ea7c";
}
.icon-refresh-left-square:before {
  content: "\ea7d";
}
.icon-refresh-right-square:before {
  content: "\ea7e";
}
.icon-refresh-square-2:before {
  content: "\ea7f";
}
.icon-refresh:before {
  content: "\ea80";
}
.icon-repeat-circle:before {
  content: "\ea81";
}
.icon-repeat:before {
  content: "\ea82";
}
.icon-direct-notification:before {
  content: "\ea83";
}
.icon-direct-right:before {
  content: "\ea84";
}
.icon-direct-send:before {
  content: "\ea85";
}
.icon-direct-up:before {
  content: "\ea86";
}
.icon-direct:before {
  content: "\ea87";
}
.icon-directbox-default:before {
  content: "\ea88";
}
.icon-directbox-notif:before {
  content: "\ea89";
}
.icon-directbox-receive:before {
  content: "\ea8a";
}
.icon-directbox-send:before {
  content: "\ea8b";
}
.icon-discount-circle:before {
  content: "\ea8c";
}
.icon-discount-shape:before {
  content: "\ea8d";
}
.icon-discover-1:before {
  content: "\ea8e";
}
.icon-discover:before {
  content: "\ea8f";
}
.icon-dislike:before {
  content: "\ea90";
}
.icon-gallery-favorite:before {
  content: "\ea91";
}
.icon-gallery-import:before {
  content: "\ea92";
}
.icon-gallery-remove:before {
  content: "\ea93";
}
.icon-gallery-slash:before {
  content: "\ea94";
}
.icon-gallery-tick:before {
  content: "\ea95";
}
.icon-gallery:before {
  content: "\ea96";
}
.icon-game:before {
  content: "\ea97";
}
.icon-gameboy:before {
  content: "\ea98";
}
.icon-gas-station:before {
  content: "\ea99";
}
.icon-gemini-2:before {
  content: "\ea9a";
}
.icon-gemini:before {
  content: "\ea9b";
}
.icon-ghost:before {
  content: "\ea9c";
}
.icon-gift:before {
  content: "\ea9d";
}
.icon-glass-1:before {
  content: "\ea9e";
}
.icon-glass:before {
  content: "\ea9f";
}
.icon-global-edit:before {
  content: "\eaa0";
}
.icon-global-refresh:before {
  content: "\eaa1";
}
.icon-global-search:before {
  content: "\eaa2";
}
.icon-global:before {
  content: "\eaa3";
}
.icon-gps-slash:before {
  content: "\eaa4";
}
.icon-gps:before {
  content: "\eaa5";
}
.icon-grammerly:before {
  content: "\eaa6";
}
.icon-graph:before {
  content: "\eaa7";
}
.icon-grid-1:before {
  content: "\eaa8";
}
.icon-grid-2:before {
  content: "\eaa9";
}
.icon-grid-3:before {
  content: "\eaaa";
}
.icon-grid-4:before {
  content: "\eaab";
}
.icon-grid-5:before {
  content: "\eaac";
}
.icon-grid-6:before {
  content: "\eaad";
}
.icon-grid-7:before {
  content: "\eaae";
}
.icon-grid-8:before {
  content: "\eaaf";
}
.icon-grid-9:before {
  content: "\eab0";
}
.icon-grid-edit:before {
  content: "\eab1";
}
.icon-grid-eraser:before {
  content: "\eab2";
}
.icon-grid-lock:before {
  content: "\eab3";
}
.icon-happyemoji:before {
  content: "\eab4";
}
.icon-hashtag-1:before {
  content: "\eab5";
}
.icon-hashtag-down:before {
  content: "\eab6";
}
.icon-hashtag-up:before {
  content: "\eab7";
}
.icon-hashtag:before {
  content: "\eab8";
}
.icon-headphone:before {
  content: "\eab9";
}
.icon-headphones:before {
  content: "\eaba";
}
.icon-health:before {
  content: "\eabb";
}
.icon-heart-add:before {
  content: "\eabc";
}
.icon-heart-circle:before {
  content: "\eabd";
}
.icon-heart-edit:before {
  content: "\eabe";
}
.icon-heart-remove:before {
  content: "\eabf";
}
.icon-heart-search:before {
  content: "\eac0";
}
.icon-heart-slash:before {
  content: "\eac1";
}
.icon-heart-tick:before {
  content: "\eac2";
}
.icon-heart:before {
  content: "\eac3";
}
.icon-hierarchy-2:before {
  content: "\eac4";
}
.icon-hierarchy-3:before {
  content: "\eac5";
}
.icon-hierarchy-square-2:before {
  content: "\eac6";
}
.icon-hierarchy-square-3:before {
  content: "\eac7";
}
.icon-hierarchy-square:before {
  content: "\eac8";
}
.icon-hierarchy:before {
  content: "\eac9";
}
.icon-home-1:before {
  content: "\eaca";
}
.icon-home-2:before {
  content: "\eacb";
}
.icon-home-hashtag:before {
  content: "\eacc";
}
.icon-home-trend-down:before {
  content: "\eacd";
}
.icon-home-trend-up:before {
  content: "\eace";
}
.icon-home-wifi:before {
  content: "\eacf";
}
.icon-home:before {
  content: "\ead0";
}
.icon-hospital:before {
  content: "\ead1";
}
.icon-house-2:before {
  content: "\ead2";
}
.icon-house:before {
  content: "\ead3";
}
.icon-image:before {
  content: "\ead4";
}
.icon-import-1:before {
  content: "\ead5";
}
.icon-import-2:before {
  content: "\ead6";
}
.icon-import-3:before {
  content: "\ead7";
}
.icon-import:before {
  content: "\ead8";
}
.icon-info-circle:before {
  content: "\ead9";
}
.icon-information:before {
  content: "\eada";
}
.icon-instagram:before {
  content: "\eadb";
}
.icon-judge:before {
  content: "\eadc";
}
.icon-kanban:before {
  content: "\eadd";
}
.icon-key-square:before {
  content: "\eade";
}
.icon-key:before {
  content: "\eadf";
}
.icon-keyboard-open:before {
  content: "\eae0";
}
.icon-keyboard:before {
  content: "\eae1";
}
.icon-lamp-1:before {
  content: "\eae2";
}
.icon-lamp-charge:before {
  content: "\eae3";
}
.icon-lamp-on:before {
  content: "\eae4";
}
.icon-lamp-slash:before {
  content: "\eae5";
}
.icon-lamp:before {
  content: "\eae6";
}
.icon-language-circle:before {
  content: "\eae7";
}
.icon-language-square:before {
  content: "\eae8";
}
.icon-layer:before {
  content: "\eae9";
}
.icon-level:before {
  content: "\eaea";
}
.icon-lifebuoy:before {
  content: "\eaeb";
}
.icon-like-1:before {
  content: "\eaec";
}
.icon-like-dislike:before {
  content: "\eaed";
}
.icon-like-shapes:before {
  content: "\eaee";
}
.icon-like-tag:before {
  content: "\eaef";
}
.icon-like:before {
  content: "\eaf0";
}
.icon-link-1:before {
  content: "\eaf1";
}
.icon-link-2:before {
  content: "\eaf2";
}
.icon-link-21:before {
  content: "\eaf3";
}
.icon-link-circle:before {
  content: "\eaf4";
}
.icon-link-square:before {
  content: "\eaf5";
}
.icon-link:before {
  content: "\eaf6";
}
.icon-location-add:before {
  content: "\eaf7";
}
.icon-location-cross:before {
  content: "\eaf8";
}
.icon-location-minus:before {
  content: "\eaf9";
}
.icon-location-slash:before {
  content: "\eafa";
}
.icon-location-tick:before {
  content: "\eafb";
}
.icon-location:before {
  content: "\eafc";
}
.icon-lock-1:before {
  content: "\eafd";
}
.icon-lock-circle:before {
  content: "\eafe";
}
.icon-lock-slash:before {
  content: "\eaff";
}
.icon-lock:before {
  content: "\eb00";
}
.icon-login-1:before {
  content: "\eb01";
}
.icon-login:before {
  content: "\eb02";
}
.icon-logout-1:before {
  content: "\eb03";
}
.icon-logout:before {
  content: "\eb04";
}
.icon-lovely:before {
  content: "\eb05";
}
.icon-magic-star:before {
  content: "\eb06";
}
.icon-magicpen:before {
  content: "\eb07";
}
.icon-main-component:before {
  content: "\eb08";
}
.icon-man:before {
  content: "\eb09";
}
.icon-map-1:before {
  content: "\eb0a";
}
.icon-map:before {
  content: "\eb0b";
}
.icon-mask-1:before {
  content: "\eb0c";
}
.icon-mask-2:before {
  content: "\eb0d";
}
.icon-mask:before {
  content: "\eb0e";
}
.icon-math:before {
  content: "\eb0f";
}
.icon-maximize-1:before {
  content: "\eb10";
}
.icon-maximize-2:before {
  content: "\eb11";
}
.icon-maximize-3:before {
  content: "\eb12";
}
.icon-maximize-4:before {
  content: "\eb13";
}
.icon-maximize-21:before {
  content: "\eb14";
}
.icon-maximize-circle:before {
  content: "\eb15";
}
.icon-maximize:before {
  content: "\eb16";
}
.icon-medal-star:before {
  content: "\eb17";
}
.icon-medal:before {
  content: "\eb18";
}
.icon-menu-1:before {
  content: "\eb19";
}
.icon-menu-board:before {
  content: "\eb1a";
}
.icon-menu:before {
  content: "\eb1b";
}
.icon-message-2:before {
  content: "\eb1c";
}
.icon-message-add-1:before {
  content: "\eb1d";
}
.icon-card-slash:before {
  content: "\eb1e";
}
.icon-card-tick-1:before {
  content: "\eb1f";
}
.icon-card-tick:before {
  content: "\eb20";
}
.icon-card:before {
  content: "\eb21";
}
.icon-cards:before {
  content: "\eb22";
}
.icon-category-2:before {
  content: "\eb23";
}
.icon-category:before {
  content: "\eb24";
}
.icon-cd:before {
  content: "\eb25";
}
.icon-chart-1:before {
  content: "\eb26";
}
.icon-chart-2:before {
  content: "\eb27";
}
.icon-chart-3:before {
  content: "\eb28";
}
.icon-chart-21:before {
  content: "\eb29";
}
.icon-chart-fail:before {
  content: "\eb2a";
}
.icon-chart-square:before {
  content: "\eb2b";
}
.icon-chart-success:before {
  content: "\eb2c";
}
.icon-chart:before {
  content: "\eb2d";
}
.icon-check:before {
  content: "\eb2e";
}
.icon-chrome:before {
  content: "\eb2f";
}
.icon-clipboard-close:before {
  content: "\eb30";
}
.icon-clipboard-export:before {
  content: "\eb31";
}
.icon-clipboard-import:before {
  content: "\eb32";
}
.icon-clipboard-text:before {
  content: "\eb33";
}
.icon-clipboard-tick:before {
  content: "\eb34";
}
.icon-clipboard:before {
  content: "\eb35";
}
.icon-clock-1:before {
  content: "\eb36";
}
.icon-clock:before {
  content: "\eb37";
}
.icon-close-circle:before {
  content: "\eb38";
}
.icon-close-square:before {
  content: "\eb39";
}
.icon-cloud-add:before {
  content: "\eb3a";
}
.icon-cloud-change:before {
  content: "\eb3b";
}
.icon-cloud-connection:before {
  content: "\eb3c";
}
.icon-cloud-cross:before {
  content: "\eb3d";
}
.icon-cloud-drizzle:before {
  content: "\eb3e";
}
.icon-cloud-fog:before {
  content: "\eb3f";
}
.icon-cloud-lightning:before {
  content: "\eb40";
}
.icon-cloud-minus:before {
  content: "\eb41";
}
.icon-cloud-notif:before {
  content: "\eb42";
}
.icon-cloud-plus:before {
  content: "\eb43";
}
.icon-cloud-remove:before {
  content: "\eb44";
}
.icon-cloud-snow:before {
  content: "\eb45";
}
.icon-cloud-sunny:before {
  content: "\eb46";
}
.icon-cloud:before {
  content: "\eb47";
}
.icon-code-1:before {
  content: "\eb48";
}
.icon-code-circle:before {
  content: "\eb49";
}
.icon-code:before {
  content: "\eb4a";
}
.icon-coffee:before {
  content: "\eb4b";
}
.icon-coin-1:before {
  content: "\eb4c";
}
.icon-coin:before {
  content: "\eb4d";
}
.icon-color-swatch:before {
  content: "\eb4e";
}
.icon-colorfilter:before {
  content: "\eb4f";
}
.icon-colors-square:before {
  content: "\eb50";
}
.icon-command-square:before {
  content: "\eb51";
}
.icon-command:before {
  content: "\eb52";
}
.icon-component:before {
  content: "\eb53";
}
.icon-computing:before {
  content: "\eb54";
}
.icon-convert-3d-cube:before {
  content: "\eb55";
}
.icon-convert-card:before {
  content: "\eb56";
}
.icon-convert:before {
  content: "\eb57";
}
.icon-convertshape-2:before {
  content: "\eb58";
}
.icon-convertshape:before {
  content: "\eb59";
}
.icon-copy-success:before {
  content: "\eb5a";
}
.icon-copy:before {
  content: "\eb5b";
}
.icon-copyright:before {
  content: "\eb5c";
}
.icon-courthouse:before {
  content: "\eb5d";
}
.icon-cpu-charge:before {
  content: "\eb5e";
}
.icon-cpu-setting:before {
  content: "\eb5f";
}
.icon-cpu:before {
  content: "\eb60";
}
.icon-creative-commons:before {
  content: "\eb61";
}
.icon-crop:before {
  content: "\eb62";
}
.icon-crown-1:before {
  content: "\eb63";
}
.icon-crown:before {
  content: "\eb64";
}
.icon-cup:before {
  content: "\eb65";
}
.icon-danger:before {
  content: "\eb66";
}
.icon-data-2:before {
  content: "\eb67";
}
.icon-data:before {
  content: "\eb68";
}
.icon-designtools:before {
  content: "\eb69";
}
.icon-device-message:before {
  content: "\eb6a";
}
.icon-devices-1:before {
  content: "\eb6b";
}
.icon-devices:before {
  content: "\eb6c";
}
.icon-diagram:before {
  content: "\eb6d";
}
.icon-diamonds:before {
  content: "\eb6e";
}
.icon-direct-down:before {
  content: "\eb6f";
}
.icon-direct-inbox:before {
  content: "\eb70";
}
.icon-direct-left:before {
  content: "\eb71";
}
.icon-direct-normal:before {
  content: "\eb72";
}
.icon-document-1:before {
  content: "\eb73";
}
.icon-document-cloud:before {
  content: "\eb74";
}
.icon-document-code-2:before {
  content: "\eb75";
}
.icon-document-code:before {
  content: "\eb76";
}
.icon-document-copy:before {
  content: "\eb77";
}
.icon-document-download:before {
  content: "\eb78";
}
.icon-document-favorite:before {
  content: "\eb79";
}
.icon-document-filter:before {
  content: "\eb7a";
}
.icon-document-forward:before {
  content: "\eb7b";
}
.icon-document-like:before {
  content: "\eb7c";
}
.icon-document-normal:before {
  content: "\eb7d";
}
.icon-document-previous:before {
  content: "\eb7e";
}
.icon-document-sketch:before {
  content: "\eb7f";
}
.icon-document-text-1:before {
  content: "\eb80";
}
.icon-document-text:before {
  content: "\eb81";
}
.icon-document-upload:before {
  content: "\eb82";
}
.icon-document:before {
  content: "\eb83";
}
.icon-dollar-circle:before {
  content: "\eb84";
}
.icon-dollar-square:before {
  content: "\eb85";
}
.icon-driver-2:before {
  content: "\eb86";
}
.icon-driver-refresh:before {
  content: "\eb87";
}
.icon-driver:before {
  content: "\eb88";
}
.icon-driving:before {
  content: "\eb89";
}
.icon-drop:before {
  content: "\eb8a";
}
.icon-edit-2:before {
  content: "\eb8b";
}
.icon-edit:before {
  content: "\eb8c";
}
.icon-electricity:before {
  content: "\eb8d";
}
.icon-element-2:before {
  content: "\eb8e";
}
.icon-element-3:before {
  content: "\eb8f";
}
.icon-element-4:before {
  content: "\eb90";
}
.icon-element-equal:before {
  content: "\eb91";
}
.icon-element-plus:before {
  content: "\eb92";
}
.icon-emoji-happy:before {
  content: "\eb93";
}
.icon-emoji-normal:before {
  content: "\eb94";
}
.icon-emoji-sad:before {
  content: "\eb95";
}
.icon-empty-wallet-add:before {
  content: "\eb96";
}
.icon-empty-wallet-change:before {
  content: "\eb97";
}
.icon-empty-wallet-remove:before {
  content: "\eb98";
}
.icon-empty-wallet-tick:before {
  content: "\eb99";
}
.icon-empty-wallet-time:before {
  content: "\eb9a";
}
.icon-empty-wallet:before {
  content: "\eb9b";
}
.icon-eraser-1:before {
  content: "\eb9c";
}
.icon-eraser:before {
  content: "\eb9d";
}
.icon-export-1:before {
  content: "\eb9e";
}
.icon-export-2:before {
  content: "\eb9f";
}
.icon-export-3:before {
  content: "\eba0";
}
.icon-export:before {
  content: "\eba1";
}
.icon-external-drive:before {
  content: "\eba2";
}
.icon-eye-slash:before {
  content: "\eba3";
}
.icon-eye:before {
  content: "\eba4";
}
.icon-fatrows:before {
  content: "\eba5";
}
.icon-favorite-chart:before {
  content: "\eba6";
}
.icon-filter-add:before {
  content: "\eba7";
}
.icon-filter-edit:before {
  content: "\eba8";
}
.icon-filter-remove:before {
  content: "\eba9";
}
.icon-filter-search:before {
  content: "\ebaa";
}
.icon-filter-square:before {
  content: "\ebab";
}
.icon-filter-tick:before {
  content: "\ebac";
}
.icon-filter:before {
  content: "\ebad";
}
.icon-finger-cricle:before {
  content: "\ebae";
}
.icon-finger-scan:before {
  content: "\ebaf";
}
.icon-firstline:before {
  content: "\ebb0";
}
.icon-flag-2:before {
  content: "\ebb1";
}
.icon-flag:before {
  content: "\ebb2";
}
.icon-flash-1:before {
  content: "\ebb3";
}
.icon-flash-circle-1:before {
  content: "\ebb4";
}
.icon-flash-circle:before {
  content: "\ebb5";
}
.icon-flash-slash:before {
  content: "\ebb6";
}
.icon-flash:before {
  content: "\ebb7";
}
.icon-folder-2:before {
  content: "\ebb8";
}
.icon-folder-add:before {
  content: "\ebb9";
}
.icon-folder-cloud:before {
  content: "\ebba";
}
.icon-folder-connection:before {
  content: "\ebbb";
}
.icon-folder-cross:before {
  content: "\ebbc";
}
.icon-folder-favorite:before {
  content: "\ebbd";
}
.icon-folder-minus:before {
  content: "\ebbe";
}
.icon-folder-open:before {
  content: "\ebbf";
}
.icon-folder:before {
  content: "\ebc0";
}
.icon-forbidden-2:before {
  content: "\ebc1";
}
.icon-forbidden:before {
  content: "\ebc2";
}
.icon-format-circle:before {
  content: "\ebc3";
}
.icon-format-square:before {
  content: "\ebc4";
}
.icon-forward-5-seconds:before {
  content: "\ebc5";
}
.icon-forward-10-seconds:before {
  content: "\ebc6";
}
.icon-forward-15-seconds:before {
  content: "\ebc7";
}
.icon-forward-item:before {
  content: "\ebc8";
}
.icon-forward-square:before {
  content: "\ebc9";
}
.icon-forward:before {
  content: "\ebca";
}
.icon-frame-1:before {
  content: "\ebcb";
}
.icon-frame-2:before {
  content: "\ebcc";
}
.icon-frame-3:before {
  content: "\ebcd";
}
.icon-frame-4:before {
  content: "\ebce";
}
.icon-frame:before {
  content: "\ebcf";
}
.icon-gallery-add:before {
  content: "\ebd0";
}
.icon-gallery-edit:before {
  content: "\ebd1";
}
.icon-gallery-export:before {
  content: "\ebd2";
}
.icon-d-cube-scan:before {
  content: "\ebd3";
}
.icon-d-rotate:before {
  content: "\ebd4";
}
.icon-d-square:before {
  content: "\ebd5";
}
.icon-dcube:before {
  content: "\ebd6";
}
.icon-square:before {
  content: "\ebd7";
}
.icon-support:before {
  content: "\ebd8";
}
.icon-activity:before {
  content: "\ebd9";
}
.icon-add-circle:before {
  content: "\ebda";
}
.icon-add-square:before {
  content: "\ebdb";
}
.icon-add:before {
  content: "\ebdc";
}
.icon-additem:before {
  content: "\ebdd";
}
.icon-airdrop:before {
  content: "\ebde";
}
.icon-airplane-square:before {
  content: "\ebdf";
}
.icon-airplane:before {
  content: "\ebe0";
}
.icon-airpod:before {
  content: "\ebe1";
}
.icon-airpods:before {
  content: "\ebe2";
}
.icon-alarm:before {
  content: "\ebe3";
}
.icon-align-bottom:before {
  content: "\ebe4";
}
.icon-align-horizontally:before {
  content: "\ebe5";
}
.icon-align-left:before {
  content: "\ebe6";
}
.icon-align-right:before {
  content: "\ebe7";
}
.icon-align-vertically:before {
  content: "\ebe8";
}
.icon-aquarius:before {
  content: "\ebe9";
}
.icon-archive-1:before {
  content: "\ebea";
}
.icon-archive-2:before {
  content: "\ebeb";
}
.icon-archive-add:before {
  content: "\ebec";
}
.icon-archive-book:before {
  content: "\ebed";
}
.icon-archive-minus:before {
  content: "\ebee";
}
.icon-archive-slash:before {
  content: "\ebef";
}
.icon-archive-tick:before {
  content: "\ebf0";
}
.icon-archive:before {
  content: "\ebf1";
}
.icon-arrange-circle-2:before {
  content: "\ebf2";
}
.icon-arrange-circle:before {
  content: "\ebf3";
}
.icon-arrange-square-2:before {
  content: "\ebf4";
}
.icon-arrange-square:before {
  content: "\ebf5";
}
.icon-arrow-2:before {
  content: "\ebf6";
}
.icon-arrow-3:before {
  content: "\ebf7";
}
.icon-arrow-bottom:before {
  content: "\ebf8";
}
.icon-arrow-circle-down:before {
  content: "\ebf9";
}
.icon-arrow-circle-left:before {
  content: "\ebfa";
}
.icon-arrow-circle-right:before {
  content: "\ebfb";
}
.icon-arrow-circle-up:before {
  content: "\ebfc";
}
.icon-arrow-down-1:before {
  content: "\ebfd";
}
.icon-arrow-down-2:before {
  content: "\ebfe";
}
.icon-arrow-down:before {
  content: "\ebff";
}
.icon-arrow-left-1:before {
  content: "\ec00";
}
.icon-arrow-left-2:before {
  content: "\ec01";
}
.icon-arrow-left-3:before {
  content: "\ec02";
}
.icon-arrow-left:before {
  content: "\ec03";
}
.icon-arrow-right-1:before {
  content: "\ec04";
}
.icon-arrow-right-2:before {
  content: "\ec05";
}
.icon-arrow-right-3:before {
  content: "\ec06";
}
.icon-arrow-right:before {
  content: "\ec07";
}
.icon-arrow-square-down:before {
  content: "\ec08";
}
.icon-arrow-square-left:before {
  content: "\ec09";
}
.icon-arrow-square-right:before {
  content: "\ec0a";
}
.icon-arrow-square-up:before {
  content: "\ec0b";
}
.icon-arrow-square:before {
  content: "\ec0c";
}
.icon-arrow-swap-horizontal:before {
  content: "\ec0d";
}
.icon-arrow-swap:before {
  content: "\ec0e";
}
.icon-arrow-up-1:before {
  content: "\ec0f";
}
.icon-arrow-up-2:before {
  content: "\ec10";
}
.icon-arrow-up-3:before {
  content: "\ec11";
}
.icon-arrow-up:before {
  content: "\ec12";
}
.icon-arrow:before {
  content: "\ec13";
}
.icon-attach-circle:before {
  content: "\ec14";
}
.icon-attach-square:before {
  content: "\ec15";
}
.icon-audio-square:before {
  content: "\ec16";
}
.icon-autobrightness:before {
  content: "\ec17";
}
.icon-award:before {
  content: "\ec18";
}
.icon-back-square:before {
  content: "\ec19";
}
.icon-backward-5-seconds:before {
  content: "\ec1a";
}
.icon-backward-10-seconds:before {
  content: "\ec1b";
}
.icon-backward-15-seconds:before {
  content: "\ec1c";
}
.icon-backward-item:before {
  content: "\ec1d";
}
.icon-backward:before {
  content: "\ec1e";
}
.icon-bag-2:before {
  content: "\ec1f";
}
.icon-bag-cross-1:before {
  content: "\ec20";
}
.icon-bag-cross:before {
  content: "\ec21";
}
.icon-bag-happy:before {
  content: "\ec22";
}
.icon-bag-tick-2:before {
  content: "\ec23";
}
.icon-bag-tick:before {
  content: "\ec24";
}
.icon-bag-timer:before {
  content: "\ec25";
}
.icon-bag:before {
  content: "\ec26";
}
.icon-bank:before {
  content: "\ec27";
}
.icon-barcode:before {
  content: "\ec28";
}
.icon-battery-3full:before {
  content: "\ec29";
}
.icon-battery-charging:before {
  content: "\ec2a";
}
.icon-battery-disable:before {
  content: "\ec2b";
}
.icon-battery-empty-1:before {
  content: "\ec2c";
}
.icon-battery-empty:before {
  content: "\ec2d";
}
.icon-battery-full:before {
  content: "\ec2e";
}
.icon-bezier:before {
  content: "\ec2f";
}
.icon-bill:before {
  content: "\ec30";
}
.icon-bitcoin-card:before {
  content: "\ec31";
}
.icon-bitcoin-convert:before {
  content: "\ec32";
}
.icon-bitcoin-refresh:before {
  content: "\ec33";
}
.icon-blend-2:before {
  content: "\ec34";
}
.icon-blend:before {
  content: "\ec35";
}
.icon-bluetooth-2:before {
  content: "\ec36";
}
.icon-bluetooth-circle:before {
  content: "\ec37";
}
.icon-bluetooth-rectangle:before {
  content: "\ec38";
}
.icon-bluetooth:before {
  content: "\ec39";
}
.icon-blur:before {
  content: "\ec3a";
}
.icon-book-1:before {
  content: "\ec3b";
}
.icon-book-saved:before {
  content: "\ec3c";
}
.icon-book-square:before {
  content: "\ec3d";
}
.icon-book:before {
  content: "\ec3e";
}
.icon-bookmark-2:before {
  content: "\ec3f";
}
.icon-bookmark:before {
  content: "\ec40";
}
.icon-box-1:before {
  content: "\ec41";
}
.icon-box-2:before {
  content: "\ec42";
}
.icon-box-add:before {
  content: "\ec43";
}
.icon-box-remove:before {
  content: "\ec44";
}
.icon-box-search:before {
  content: "\ec45";
}
.icon-box-tick:before {
  content: "\ec46";
}
.icon-box-time:before {
  content: "\ec47";
}
.icon-box:before {
  content: "\ec48";
}
.icon-briefcase:before {
  content: "\ec49";
}
.icon-brifecase-cross:before {
  content: "\ec4a";
}
.icon-brifecase-tick:before {
  content: "\ec4b";
}
.icon-brifecase-timer:before {
  content: "\ec4c";
}
.icon-broom:before {
  content: "\ec4d";
}
.icon-brush-1:before {
  content: "\ec4e";
}
.icon-brush-2:before {
  content: "\ec4f";
}
.icon-brush-3:before {
  content: "\ec50";
}
.icon-brush-4:before {
  content: "\ec51";
}
.icon-brush:before {
  content: "\ec52";
}
.icon-bubble:before {
  content: "\ec53";
}
.icon-bucket-circle:before {
  content: "\ec54";
}
.icon-bucket-square:before {
  content: "\ec55";
}
.icon-bucket:before {
  content: "\ec56";
}
.icon-building-3:before {
  content: "\ec57";
}
.icon-building-4:before {
  content: "\ec58";
}
.icon-building:before {
  content: "\ec59";
}
.icon-buildings-2:before {
  content: "\ec5a";
}
.icon-buildings:before {
  content: "\ec5b";
}
.icon-buliding:before {
  content: "\ec5c";
}
.icon-bus:before {
  content: "\ec5d";
}
.icon-buy-crypto:before {
  content: "\ec5e";
}
.icon-cake:before {
  content: "\ec5f";
}
.icon-calculator:before {
  content: "\ec60";
}
.icon-calendar-1:before {
  content: "\ec61";
}
.icon-calendar-2:before {
  content: "\ec62";
}
.icon-calendar-add:before {
  content: "\ec63";
}
.icon-calendar-circle:before {
  content: "\ec64";
}
.icon-calendar-edit:before {
  content: "\ec65";
}
.icon-calendar-remove:before {
  content: "\ec66";
}
.icon-calendar-search:before {
  content: "\ec67";
}
.icon-calendar-tick:before {
  content: "\ec68";
}
.icon-calendar:before {
  content: "\ec69";
}
.icon-call-add:before {
  content: "\ec6a";
}
.icon-call-calling:before {
  content: "\ec6b";
}
.icon-call-incoming:before {
  content: "\ec6c";
}
.icon-call-minus:before {
  content: "\ec6d";
}
.icon-call-outgoing:before {
  content: "\ec6e";
}
.icon-call-received:before {
  content: "\ec6f";
}
.icon-call-remove:before {
  content: "\ec70";
}
.icon-call-slash:before {
  content: "\ec71";
}
.icon-call:before {
  content: "\ec72";
}
.icon-camera-slash:before {
  content: "\ec73";
}
.icon-camera:before {
  content: "\ec74";
}
.icon-candle-2:before {
  content: "\ec75";
}
.icon-candle:before {
  content: "\ec76";
}
.icon-car:before {
  content: "\ec77";
}
.icon-card-add:before {
  content: "\ec78";
}
.icon-card-coin:before {
  content: "\ec79";
}
.icon-card-edit:before {
  content: "\ec7a";
}
.icon-card-pos:before {
  content: "\ec7b";
}
.icon-card-receive:before {
  content: "\ec7c";
}
.icon-card-remove-1:before {
  content: "\ec7d";
}
.icon-card-remove:before {
  content: "\ec7e";
}
.icon-card-send:before {
  content: "\ec7f";
}
.icon-Stroke-58:before {
  content: "\ec80";
}
.icon-Combined-Shape:before {
  content: "\ec81";
}
.icon-Shape:before {
  content: "\ec82";
  color: #6c6d74;
}
.icon-facebook:before {
  content: "\ec83";
}
.icon-envelope-solid:before {
  content: "\ec84";
}
.icon-send-sqaure-21:before {
  content: "\ec85";
}
.icon-send-square1:before {
  content: "\ec86";
}
.icon-send1:before {
  content: "\ec87";
}
.icon-setting-21:before {
  content: "\ec88";
}
.icon-setting-31:before {
  content: "\ec89";
}
.icon-setting-41:before {
  content: "\ec8a";
}
.icon-setting-51:before {
  content: "\ec8b";
}
.icon-setting1:before {
  content: "\ec8c";
}
.icon-settings1:before {
  content: "\ec8d";
}
.icon-shapes-11:before {
  content: "\ec8e";
}
.icon-shapes-2:before {
  content: "\ec8f";
}
.icon-shapes1:before {
  content: "\ec90";
}
.icon-share1:before {
  content: "\ec91";
}
.icon-shield-cross1:before {
  content: "\ec92";
}
.icon-shield-search1:before {
  content: "\ec93";
}
.icon-shield-security1:before {
  content: "\ec94";
}
.icon-shield-slash1:before {
  content: "\ec95";
}
.icon-shield-tick1:before {
  content: "\ec96";
}
.icon-shield:before {
  content: "\ec97";
}
.icon-ship1:before {
  content: "\ec98";
}
.icon-shop-add1:before {
  content: "\ec99";
}
.icon-shop-remove1:before {
  content: "\ec9a";
}
.icon-shop1:before {
  content: "\ec9b";
}
.icon-shopping-bag1:before {
  content: "\ec9c";
}
.icon-shopping-cart1:before {
  content: "\ec9d";
}
.icon-sidebar-bottom1:before {
  content: "\ec9e";
}
.icon-sidebar-left1:before {
  content: "\ec9f";
}
.icon-sidebar-right1:before {
  content: "\eca0";
}
.icon-sidebar-top1:before {
  content: "\eca1";
}
.icon-signpost1:before {
  content: "\eca2";
}
.icon-simcard-11:before {
  content: "\eca3";
}
.icon-simcard-21:before {
  content: "\eca4";
}
.icon-simcard1:before {
  content: "\eca5";
}
.icon-size1:before {
  content: "\eca6";
}
.icon-slash1:before {
  content: "\eca7";
}
.icon-slider-horizontal-11:before {
  content: "\eca8";
}
.icon-slider-horizontal1:before {
  content: "\eca9";
}
.icon-slider-vertical-11:before {
  content: "\ecaa";
}
.icon-slider-vertical1:before {
  content: "\ecab";
}
.icon-slider1:before {
  content: "\ecac";
}
.icon-smallcaps1:before {
  content: "\ecad";
}
.icon-smart-car1:before {
  content: "\ecae";
}
.icon-smart-home1:before {
  content: "\ecaf";
}
.icon-smileys1:before {
  content: "\ecb0";
}
.icon-sms-edit1:before {
  content: "\ecb1";
}
.icon-sms-notification1:before {
  content: "\ecb2";
}
.icon-sms-search1:before {
  content: "\ecb3";
}
.icon-sms-star1:before {
  content: "\ecb4";
}
.icon-sms-tracking1:before {
  content: "\ecb5";
}
.icon-sms1:before {
  content: "\ecb6";
}
.icon-sort1:before {
  content: "\ecb7";
}
.icon-sound1:before {
  content: "\ecb8";
}
.icon-speaker1:before {
  content: "\ecb9";
}
.icon-speedometer1:before {
  content: "\ecba";
}
.icon-star-11:before {
  content: "\ecbb";
}
.icon-star-slash1:before {
  content: "\ecbc";
}
.icon-star1:before {
  content: "\ecbd";
}
.icon-status-up1:before {
  content: "\ecbe";
}
.icon-status1:before {
  content: "\ecbf";
}
.icon-sticker1:before {
  content: "\ecc0";
}
.icon-stickynote1:before {
  content: "\ecc1";
}
.icon-stop-circle1:before {
  content: "\ecc2";
}
.icon-stop1:before {
  content: "\ecc3";
}
.icon-story1:before {
  content: "\ecc4";
}
.icon-strongbox-21:before {
  content: "\ecc5";
}
.icon-strongbox1:before {
  content: "\ecc6";
}
.icon-subtitle1:before {
  content: "\ecc7";
}
.icon-sun-11:before {
  content: "\ecc8";
}
.icon-sun-fog1:before {
  content: "\ecc9";
}
.icon-sun1:before {
  content: "\ecca";
}
.icon-tag-21:before {
  content: "\eccb";
}
.icon-tag-cross1:before {
  content: "\eccc";
}
.icon-tag-right1:before {
  content: "\eccd";
}
.icon-tag-user1:before {
  content: "\ecce";
}
.icon-tag1:before {
  content: "\eccf";
}
.icon-task-square1:before {
  content: "\ecd0";
}
.icon-task1:before {
  content: "\ecd1";
}
.icon-teacher1:before {
  content: "\ecd2";
}
.icon-text-block1:before {
  content: "\ecd3";
}
.icon-text-bold1:before {
  content: "\ecd4";
}
.icon-text-italic1:before {
  content: "\ecd5";
}
.icon-text-underline1:before {
  content: "\ecd6";
}
.icon-text1:before {
  content: "\ecd7";
}
.icon-textalign-center1:before {
  content: "\ecd8";
}
.icon-textalign-justifycenter1:before {
  content: "\ecd9";
}
.icon-textalign-justifyleft1:before {
  content: "\ecda";
}
.icon-textalign-justifyright1:before {
  content: "\ecdb";
}
.icon-textalign-left1:before {
  content: "\ecdc";
}
.icon-textalign-right1:before {
  content: "\ecdd";
}
.icon-tick-circle1:before {
  content: "\ecde";
}
.icon-tick-square1:before {
  content: "\ecdf";
}
.icon-ticket-21:before {
  content: "\ece0";
}
.icon-ticket-discount1:before {
  content: "\ece1";
}
.icon-ticket-expired1:before {
  content: "\ece2";
}
.icon-ticket-star1:before {
  content: "\ece3";
}
.icon-ticket1:before {
  content: "\ece4";
}
.icon-timer-11:before {
  content: "\ece5";
}
.icon-timer-pause1:before {
  content: "\ece6";
}
.icon-timer-start1:before {
  content: "\ece7";
}
.icon-timer1:before {
  content: "\ece8";
}
.icon-toggle-off-circle1:before {
  content: "\ece9";
}
.icon-toggle-off1:before {
  content: "\ecea";
}
.icon-toggle-on-circle1:before {
  content: "\eceb";
}
.icon-toggle-on1:before {
  content: "\ecec";
}
.icon-trade1:before {
  content: "\eced";
}
.icon-transaction-minus1:before {
  content: "\ecee";
}
.icon-translate1:before {
  content: "\ecef";
}
.icon-trash1:before {
  content: "\ecf0";
}
.icon-tree1:before {
  content: "\ecf1";
}
.icon-trend-down1:before {
  content: "\ecf2";
}
.icon-trend-up1:before {
  content: "\ecf3";
}
.icon-triangle1:before {
  content: "\ecf4";
}
.icon-truck-remove1:before {
  content: "\ecf5";
}
.icon-truck-tick1:before {
  content: "\ecf6";
}
.icon-truck-time1:before {
  content: "\ecf7";
}
.icon-trush-square1:before {
  content: "\ecf8";
}
.icon-undo1:before {
  content: "\ecf9";
}
.icon-unlimited1:before {
  content: "\ecfa";
}
.icon-unlock1:before {
  content: "\ecfb";
}
.icon-user-add1:before {
  content: "\ecfc";
}
.icon-user-cirlce-add1:before {
  content: "\ecfd";
}
.icon-user-edit1:before {
  content: "\ecfe";
}
.icon-user-minus1:before {
  content: "\ecff";
}
.icon-user-octagon1:before {
  content: "\ed00";
}
.icon-user-remove1:before {
  content: "\ed01";
}
.icon-user-search1:before {
  content: "\ed02";
}
.icon-user-square1:before {
  content: "\ed03";
}
.icon-user-tag1:before {
  content: "\ed04";
}
.icon-user-tick1:before {
  content: "\ed05";
}
.icon-user1:before {
  content: "\ed06";
}
.icon-verify1:before {
  content: "\ed07";
}
.icon-video-add1:before {
  content: "\ed08";
}
.icon-video-circle1:before {
  content: "\ed09";
}
.icon-video-horizontal1:before {
  content: "\ed0a";
}
.icon-video-octagon1:before {
  content: "\ed0b";
}
.icon-video-play1:before {
  content: "\ed0c";
}
.icon-video-remove1:before {
  content: "\ed0d";
}
.icon-video-slash1:before {
  content: "\ed0e";
}
.icon-video-square1:before {
  content: "\ed0f";
}
.icon-video-tick1:before {
  content: "\ed10";
}
.icon-video-time1:before {
  content: "\ed11";
}
.icon-video-vertical1:before {
  content: "\ed12";
}
.icon-video1:before {
  content: "\ed13";
}
.icon-voice-cricle1:before {
  content: "\ed14";
}
.icon-voice-square1:before {
  content: "\ed15";
}
.icon-volume-cross1:before {
  content: "\ed16";
}
.icon-volume-high1:before {
  content: "\ed17";
}
.icon-volume-low-11:before {
  content: "\ed18";
}
.icon-volume-low1:before {
  content: "\ed19";
}
.icon-volume-mute1:before {
  content: "\ed1a";
}
.icon-volume-slash1:before {
  content: "\ed1b";
}
.icon-volume-up1:before {
  content: "\ed1c";
}
.icon-wallet-11:before {
  content: "\ed1d";
}
.icon-wallet-21:before {
  content: "\ed1e";
}
.icon-wallet-31:before {
  content: "\ed1f";
}
.icon-wallet-add-11:before {
  content: "\ed20";
}
.icon-wallet-add1:before {
  content: "\ed21";
}
.icon-wallet-check1:before {
  content: "\ed22";
}
.icon-wallet-minus1:before {
  content: "\ed23";
}
.icon-wallet-money1:before {
  content: "\ed24";
}
.icon-wallet-remove1:before {
  content: "\ed25";
}
.icon-wallet-search1:before {
  content: "\ed26";
}
.icon-wallet1:before {
  content: "\ed27";
}
.icon-warning-21:before {
  content: "\ed28";
}
.icon-watch-status1:before {
  content: "\ed29";
}
.icon-watch1:before {
  content: "\ed2a";
}
.icon-weight-11:before {
  content: "\ed2b";
}
.icon-grid-51:before {
  content: "\ed2c";
}
.icon-grid-61:before {
  content: "\ed2d";
}
.icon-grid-71:before {
  content: "\ed2e";
}
.icon-grid-81:before {
  content: "\ed2f";
}
.icon-grid-91:before {
  content: "\ed30";
}
.icon-grid-edit1:before {
  content: "\ed31";
}
.icon-grid-eraser1:before {
  content: "\ed32";
}
.icon-grid-lock1:before {
  content: "\ed33";
}
.icon-group-1:before {
  content: "\ed34";
}
.icon-group-2:before {
  content: "\ed35";
}
.icon-group-4:before {
  content: "\ed36";
}
.icon-group-5:before {
  content: "\ed37";
}
.icon-group-6:before {
  content: "\ed38";
}
.icon-group-7:before {
  content: "\ed39";
}
.icon-group-8:before {
  content: "\ed3a";
}
.icon-group-9:before {
  content: "\ed3b";
}
.icon-group-10:before {
  content: "\ed3c";
}
.icon-group-11:before {
  content: "\ed3d";
}
.icon-group:before {
  content: "\ed3e";
}
.icon-happyemoji1:before {
  content: "\ed3f";
}
.icon-hashtag-11:before {
  content: "\ed40";
}
.icon-hashtag-down1:before {
  content: "\ed41";
}
.icon-hashtag-up1:before {
  content: "\ed42";
}
.icon-hashtag1:before {
  content: "\ed43";
}
.icon-headphone1:before {
  content: "\ed44";
}
.icon-headphones1:before {
  content: "\ed45";
}
.icon-heart-add1:before {
  content: "\ed46";
}
.icon-heart-circle1:before {
  content: "\ed47";
}
.icon-heart-edit1:before {
  content: "\ed48";
}
.icon-heart-remove1:before {
  content: "\ed49";
}
.icon-heart-search1:before {
  content: "\ed4a";
}
.icon-heart-slash1:before {
  content: "\ed4b";
}
.icon-heart-tick1:before {
  content: "\ed4c";
}
.icon-heart1:before {
  content: "\ed4d";
}
.icon-music-square1:before {
  content: "\ed4e";
}
.icon-music1:before {
  content: "\ed4f";
}
.icon-musicnote1:before {
  content: "\ed50";
}
.icon-notification-status1:before {
  content: "\ed51";
}
.icon-notification1:before {
  content: "\ed52";
}
.icon-omega-circle-1:before {
  content: "\ed53";
}
.icon-pen-close-1:before {
  content: "\ed54";
}
.icon-pen-close1:before {
  content: "\ed55";
}
.icon-pen-remove-1:before {
  content: "\ed56";
}
.icon-pen-remove1:before {
  content: "\ed57";
}
.icon-pen-tool-1:before {
  content: "\ed58";
}
.icon-pen-tool-2-1:before {
  content: "\ed59";
}
.icon-pen-tool-21:before {
  content: "\ed5a";
}
.icon-pen-tool1:before {
  content: "\ed5b";
}
.icon-people1:before {
  content: "\ed5c";
}
.icon-percentage-square1:before {
  content: "\ed5d";
}
.icon-personalcard1:before {
  content: "\ed5e";
}
.icon-pet1:before {
  content: "\ed5f";
}
.icon-pharagraphspacing1:before {
  content: "\ed60";
}
.icon-picture-frame1:before {
  content: "\ed61";
}
.icon-play-add1:before {
  content: "\ed62";
}
.icon-play-circle1:before {
  content: "\ed63";
}
.icon-play-cricle1:before {
  content: "\ed64";
}
.icon-play-remove1:before {
  content: "\ed65";
}
.icon-play1:before {
  content: "\ed66";
}
.icon-presention-chart1:before {
  content: "\ed67";
}
.icon-previous1:before {
  content: "\ed68";
}
.icon-printer-slash1:before {
  content: "\ed69";
}
.icon-printer1:before {
  content: "\ed6a";
}
.icon-profile-2user1:before {
  content: "\ed6b";
}
.icon-profile-add1:before {
  content: "\ed6c";
}
.icon-profile-circle1:before {
  content: "\ed6d";
}
.icon-profile-delete1:before {
  content: "\ed6e";
}
.icon-profile-remove1:before {
  content: "\ed6f";
}
.icon-profile-tick1:before {
  content: "\ed70";
}
.icon-profile:before {
  content: "\ed71";
}
.icon-programming-arrow1:before {
  content: "\ed72";
}
.icon-programming-arrows1:before {
  content: "\ed73";
}
.icon-quote-down-circle1:before {
  content: "\ed74";
}
.icon-quote-down-square1:before {
  content: "\ed75";
}
.icon-quote-down1:before {
  content: "\ed76";
}
.icon-quote-up-circle1:before {
  content: "\ed77";
}
.icon-quote-up-square1:before {
  content: "\ed78";
}
.icon-quote-up1:before {
  content: "\ed79";
}
.icon-radar-11:before {
  content: "\ed7a";
}
.icon-radar-21:before {
  content: "\ed7b";
}
.icon-radar1:before {
  content: "\ed7c";
}
.icon-radio1:before {
  content: "\ed7d";
}
.icon-ram-21:before {
  content: "\ed7e";
}
.icon-ram1:before {
  content: "\ed7f";
}
.icon-ranking-11:before {
  content: "\ed80";
}
.icon-ranking1:before {
  content: "\ed81";
}
.icon-receipt-11:before {
  content: "\ed82";
}
.icon-receipt-2-11:before {
  content: "\ed83";
}
.icon-receipt-21:before {
  content: "\ed84";
}
.icon-receipt-add1:before {
  content: "\ed85";
}
.icon-receipt-discount1:before {
  content: "\ed86";
}
.icon-receipt-disscount1:before {
  content: "\ed87";
}
.icon-receipt-edit1:before {
  content: "\ed88";
}
.icon-receipt-item1:before {
  content: "\ed89";
}
.icon-receipt-minus1:before {
  content: "\ed8a";
}
.icon-receipt-search1:before {
  content: "\ed8b";
}
.icon-receipt-square1:before {
  content: "\ed8c";
}
.icon-receipt-text1:before {
  content: "\ed8d";
}
.icon-receipt1:before {
  content: "\ed8e";
}
.icon-receive-square-21:before {
  content: "\ed8f";
}
.icon-receive-square1:before {
  content: "\ed90";
}
.icon-received1:before {
  content: "\ed91";
}
.icon-record-circle1:before {
  content: "\ed92";
}
.icon-record1:before {
  content: "\ed93";
}
.icon-recovery-convert1:before {
  content: "\ed94";
}
.icon-redo1:before {
  content: "\ed95";
}
.icon-refresh-21:before {
  content: "\ed96";
}
.icon-refresh-circle1:before {
  content: "\ed97";
}
.icon-refresh-left-square1:before {
  content: "\ed98";
}
.icon-refresh-right-square1:before {
  content: "\ed99";
}
.icon-refresh-square-21:before {
  content: "\ed9a";
}
.icon-refresh1:before {
  content: "\ed9b";
}
.icon-repeat-circle1:before {
  content: "\ed9c";
}
.icon-repeat1:before {
  content: "\ed9d";
}
.icon-repeate-music1:before {
  content: "\ed9e";
}
.icon-repeate-one1:before {
  content: "\ed9f";
}
.icon-reserve1:before {
  content: "\eda0";
}
.icon-rotate-left-11:before {
  content: "\eda1";
}
.icon-rotate-left1:before {
  content: "\eda2";
}
.icon-rotate-right-11:before {
  content: "\eda3";
}
.icon-rotate-right1:before {
  content: "\eda4";
}
.icon-route-square1:before {
  content: "\eda5";
}
.icon-routing-21:before {
  content: "\eda6";
}
.icon-routing1:before {
  content: "\eda7";
}
.icon-row-horizontal1:before {
  content: "\eda8";
}
.icon-row-vertical1:before {
  content: "\eda9";
}
.icon-ruler1:before {
  content: "\edaa";
}
.icon-rulerpen1:before {
  content: "\edab";
}
.icon-safe-home1:before {
  content: "\edac";
}
.icon-sagittarius1:before {
  content: "\edad";
}
.icon-save-21:before {
  content: "\edae";
}
.icon-save-add1:before {
  content: "\edaf";
}
.icon-save-minus1:before {
  content: "\edb0";
}
.icon-save-remove1:before {
  content: "\edb1";
}
.icon-scan-barcode1:before {
  content: "\edb2";
}
.icon-scan1:before {
  content: "\edb3";
}
.icon-scanner1:before {
  content: "\edb4";
}
.icon-scanning1:before {
  content: "\edb5";
}
.icon-scissor-11:before {
  content: "\edb6";
}
.icon-scissor-2:before {
  content: "\edb7";
}
.icon-scissor1:before {
  content: "\edb8";
}
.icon-screenmirroring1:before {
  content: "\edb9";
}
.icon-scroll1:before {
  content: "\edba";
}
.icon-search-favorite-11:before {
  content: "\edbb";
}
.icon-search-favorite1:before {
  content: "\edbc";
}
.icon-search-normal-11:before {
  content: "\edbd";
}
.icon-search-normal1:before {
  content: "\edbe";
}
.icon-search-status-11:before {
  content: "\edbf";
}
.icon-search-status1:before {
  content: "\edc0";
}
.icon-search-zoom-in-11:before {
  content: "\edc1";
}
.icon-search-zoom-in1:before {
  content: "\edc2";
}
.icon-search-zoom-out-11:before {
  content: "\edc3";
}
.icon-search-zoom-out1:before {
  content: "\edc4";
}
.icon-security-card1:before {
  content: "\edc5";
}
.icon-security-safe1:before {
  content: "\edc6";
}
.icon-security-time1:before {
  content: "\edc7";
}
.icon-security-user1:before {
  content: "\edc8";
}
.icon-security1:before {
  content: "\edc9";
}
.icon-send-11:before {
  content: "\edca";
}
.icon-weight1:before {
  content: "\edcb";
}
.icon-wifi-square1:before {
  content: "\edcc";
}
.icon-wifi1:before {
  content: "\edcd";
}
.icon-wind-21:before {
  content: "\edce";
}
.icon-wind1:before {
  content: "\edcf";
}
.icon-woman1:before {
  content: "\edd0";
}
.icon-group-3:before {
  content: "\edd1";
}
.icon-health1:before {
  content: "\edd2";
}
.icon-hierarchy-21:before {
  content: "\edd3";
}
.icon-hierarchy-31:before {
  content: "\edd4";
}
.icon-hierarchy-square-21:before {
  content: "\edd5";
}
.icon-hierarchy-square-31:before {
  content: "\edd6";
}
.icon-hierarchy-square1:before {
  content: "\edd7";
}
.icon-hierarchy1:before {
  content: "\edd8";
}
.icon-home-21:before {
  content: "\edd9";
}
.icon-home-hashtag1:before {
  content: "\edda";
}
.icon-home-trend-down1:before {
  content: "\eddb";
}
.icon-home-trend-up1:before {
  content: "\eddc";
}
.icon-home-wifi1:before {
  content: "\eddd";
}
.icon-home1:before {
  content: "\edde";
}
.icon-hospital1:before {
  content: "\eddf";
}
.icon-house-21:before {
  content: "\ede0";
}
.icon-house1:before {
  content: "\ede1";
}
.icon-image1:before {
  content: "\ede2";
}
.icon-import-11:before {
  content: "\ede3";
}
.icon-import-21:before {
  content: "\ede4";
}
.icon-import1:before {
  content: "\ede5";
}
.icon-info-circle1:before {
  content: "\ede6";
}
.icon-information1:before {
  content: "\ede7";
}
.icon-instagram1:before {
  content: "\ede8";
}
.icon-judge1:before {
  content: "\ede9";
}
.icon-kanban1:before {
  content: "\edea";
}
.icon-key-square1:before {
  content: "\edeb";
}
.icon-key1:before {
  content: "\edec";
}
.icon-keyboard-open1:before {
  content: "\eded";
}
.icon-keyboard1:before {
  content: "\edee";
}
.icon-lamp-11:before {
  content: "\edef";
}
.icon-lamp-charge1:before {
  content: "\edf0";
}
.icon-lamp-on1:before {
  content: "\edf1";
}
.icon-lamp-slash1:before {
  content: "\edf2";
}
.icon-lamp1:before {
  content: "\edf3";
}
.icon-language-circle1:before {
  content: "\edf4";
}
.icon-language-square1:before {
  content: "\edf5";
}
.icon-layer1:before {
  content: "\edf6";
}
.icon-level1:before {
  content: "\edf7";
}
.icon-lifebuoy1:before {
  content: "\edf8";
}
.icon-like-11:before {
  content: "\edf9";
}
.icon-like-dislike1:before {
  content: "\edfa";
}
.icon-like-shapes1:before {
  content: "\edfb";
}
.icon-like-tag1:before {
  content: "\edfc";
}
.icon-like1:before {
  content: "\edfd";
}
.icon-link-11:before {
  content: "\edfe";
}
.icon-link-22:before {
  content: "\edff";
}
.icon-link-211:before {
  content: "\ee00";
}
.icon-link-circle1:before {
  content: "\ee01";
}
.icon-link-square1:before {
  content: "\ee02";
}
.icon-link1:before {
  content: "\ee03";
}
.icon-location-add1:before {
  content: "\ee04";
}
.icon-location-cross1:before {
  content: "\ee05";
}
.icon-location-minus1:before {
  content: "\ee06";
}
.icon-location-slash1:before {
  content: "\ee07";
}
.icon-location-tick1:before {
  content: "\ee08";
}
.icon-location1:before {
  content: "\ee09";
}
.icon-lock-11:before {
  content: "\ee0a";
}
.icon-lock-circle1:before {
  content: "\ee0b";
}
.icon-lock-slash1:before {
  content: "\ee0c";
}
.icon-lock1:before {
  content: "\ee0d";
}
.icon-login-11:before {
  content: "\ee0e";
}
.icon-login1:before {
  content: "\ee0f";
}
.icon-logout-11:before {
  content: "\ee10";
}
.icon-logout1:before {
  content: "\ee11";
}
.icon-lovely1:before {
  content: "\ee12";
}
.icon-magic-star1:before {
  content: "\ee13";
}
.icon-magicpen1:before {
  content: "\ee14";
}
.icon-main-component-1:before {
  content: "\ee15";
}
.icon-main-component1:before {
  content: "\ee16";
}
.icon-man1:before {
  content: "\ee17";
}
.icon-map-11:before {
  content: "\ee18";
}
.icon-map1:before {
  content: "\ee19";
}
.icon-mask-11:before {
  content: "\ee1a";
}
.icon-mask-21:before {
  content: "\ee1b";
}
.icon-mask-3:before {
  content: "\ee1c";
}
.icon-mask1:before {
  content: "\ee1d";
}
.icon-math1:before {
  content: "\ee1e";
}
.icon-maximize-11:before {
  content: "\ee1f";
}
.icon-maximize-22:before {
  content: "\ee20";
}
.icon-maximize-31:before {
  content: "\ee21";
}
.icon-maximize-41:before {
  content: "\ee22";
}
.icon-maximize-211:before {
  content: "\ee23";
}
.icon-maximize-circle1:before {
  content: "\ee24";
}
.icon-maximize1:before {
  content: "\ee25";
}
.icon-medal-star1:before {
  content: "\ee26";
}
.icon-medal1:before {
  content: "\ee27";
}
.icon-menu-11:before {
  content: "\ee28";
}
.icon-menu-board1:before {
  content: "\ee29";
}
.icon-menu1:before {
  content: "\ee2a";
}
.icon-message-21:before {
  content: "\ee2b";
}
.icon-message-add-11:before {
  content: "\ee2c";
}
.icon-message-add1:before {
  content: "\ee2d";
}
.icon-message-circle1:before {
  content: "\ee2e";
}
.icon-message-edit1:before {
  content: "\ee2f";
}
.icon-message-favorite1:before {
  content: "\ee30";
}
.icon-message-minus1:before {
  content: "\ee31";
}
.icon-message-notif1:before {
  content: "\ee32";
}
.icon-message-programming1:before {
  content: "\ee33";
}
.icon-message-question1:before {
  content: "\ee34";
}
.icon-message-remove1:before {
  content: "\ee35";
}
.icon-message-search1:before {
  content: "\ee36";
}
.icon-message-square1:before {
  content: "\ee37";
}
.icon-message-text-11:before {
  content: "\ee38";
}
.icon-message-text1:before {
  content: "\ee39";
}
.icon-message-tick1:before {
  content: "\ee3a";
}
.icon-message-time1:before {
  content: "\ee3b";
}
.icon-message1:before {
  content: "\ee3c";
}
.icon-messages-11:before {
  content: "\ee3d";
}
.icon-messages-21:before {
  content: "\ee3e";
}
.icon-messages-31:before {
  content: "\ee3f";
}
.icon-messages1:before {
  content: "\ee40";
}
.icon-microphone-21:before {
  content: "\ee41";
}
.icon-microphone-slash-11:before {
  content: "\ee42";
}
.icon-microphone-slash1:before {
  content: "\ee43";
}
.icon-microphone1:before {
  content: "\ee44";
}
.icon-microscope1:before {
  content: "\ee45";
}
.icon-milk1:before {
  content: "\ee46";
}
.icon-mini-music-sqaure1:before {
  content: "\ee47";
}
.icon-minus-cirlce1:before {
  content: "\ee48";
}
.icon-minus-square1:before {
  content: "\ee49";
}
.icon-minus1:before {
  content: "\ee4a";
}
.icon-mirror1:before {
  content: "\ee4b";
}
.icon-mirroring-screen1:before {
  content: "\ee4c";
}
.icon-mobile-programming1:before {
  content: "\ee4d";
}
.icon-mobile1:before {
  content: "\ee4e";
}
.icon-money-21:before {
  content: "\ee4f";
}
.icon-money-31:before {
  content: "\ee50";
}
.icon-money-41:before {
  content: "\ee51";
}
.icon-money-add1:before {
  content: "\ee52";
}
.icon-money-change1:before {
  content: "\ee53";
}
.icon-money-forbidden1:before {
  content: "\ee54";
}
.icon-money-recive1:before {
  content: "\ee55";
}
.icon-money-remove1:before {
  content: "\ee56";
}
.icon-money-send1:before {
  content: "\ee57";
}
.icon-money-tick1:before {
  content: "\ee58";
}
.icon-money-time1:before {
  content: "\ee59";
}
.icon-money1:before {
  content: "\ee5a";
}
.icon-moneys1:before {
  content: "\ee5b";
}
.icon-monitor-mobbile1:before {
  content: "\ee5c";
}
.icon-monitor-recorder1:before {
  content: "\ee5d";
}
.icon-monitor1:before {
  content: "\ee5e";
}
.icon-moon1:before {
  content: "\ee5f";
}
.icon-more-21:before {
  content: "\ee60";
}
.icon-more-circle1:before {
  content: "\ee61";
}
.icon-more-square1:before {
  content: "\ee62";
}
.icon-more1:before {
  content: "\ee63";
}
.icon-mouse-11:before {
  content: "\ee64";
}
.icon-mouse-circle1:before {
  content: "\ee65";
}
.icon-mouse-square1:before {
  content: "\ee66";
}
.icon-mouse1:before {
  content: "\ee67";
}
.icon-music-circle1:before {
  content: "\ee68";
}
.icon-music-dashboard1:before {
  content: "\ee69";
}
.icon-music-filter1:before {
  content: "\ee6a";
}
.icon-music-library-21:before {
  content: "\ee6b";
}
.icon-music-play1:before {
  content: "\ee6c";
}
.icon-music-playlist1:before {
  content: "\ee6d";
}
.icon-music-square-add1:before {
  content: "\ee6e";
}
.icon-music-square-remove1:before {
  content: "\ee6f";
}
.icon-music-square-search1:before {
  content: "\ee70";
}
.icon-next1:before {
  content: "\ee71";
}
.icon-note-11:before {
  content: "\ee72";
}
.icon-note-22:before {
  content: "\ee73";
}
.icon-note-211:before {
  content: "\ee74";
}
.icon-note-add1:before {
  content: "\ee75";
}
.icon-note-favorite1:before {
  content: "\ee76";
}
.icon-note-remove1:before {
  content: "\ee77";
}
.icon-note-square1:before {
  content: "\ee78";
}
.icon-note-text1:before {
  content: "\ee79";
}
.icon-note1:before {
  content: "\ee7a";
}
.icon-notification-11:before {
  content: "\ee7b";
}
.icon-notification-bing1:before {
  content: "\ee7c";
}
.icon-notification-circle:before {
  content: "\ee7d";
}
.icon-notification-favorite1:before {
  content: "\ee7e";
}
.icon-omega-circle1:before {
  content: "\ee7f";
}
.icon-omega-square-1:before {
  content: "\ee80";
}
.icon-omega-square1:before {
  content: "\ee81";
}
.icon-paintbucket1:before {
  content: "\ee82";
}
.icon-paperclip-21:before {
  content: "\ee83";
}
.icon-paperclip1:before {
  content: "\ee84";
}
.icon-password-check1:before {
  content: "\ee85";
}
.icon-path-21:before {
  content: "\ee86";
}
.icon-path-square1:before {
  content: "\ee87";
}
.icon-path1:before {
  content: "\ee88";
}
.icon-pause-circle1:before {
  content: "\ee89";
}
.icon-pause1:before {
  content: "\ee8a";
}
.icon-pen-add-1:before {
  content: "\ee8b";
}
.icon-pen-add1:before {
  content: "\ee8c";
}
.icon-battery-3full1:before {
  content: "\ee8d";
}
.icon-bus1:before {
  content: "\ee8e";
}
.icon-buy-crypto1:before {
  content: "\ee8f";
}
.icon-cake1:before {
  content: "\ee90";
}
.icon-calculator1:before {
  content: "\ee91";
}
.icon-calendar-11:before {
  content: "\ee92";
}
.icon-calendar-21:before {
  content: "\ee93";
}
.icon-calendar-add1:before {
  content: "\ee94";
}
.icon-calendar-circle1:before {
  content: "\ee95";
}
.icon-calendar-edit1:before {
  content: "\ee96";
}
.icon-calendar-remove1:before {
  content: "\ee97";
}
.icon-calendar-search1:before {
  content: "\ee98";
}
.icon-calendar-tick1:before {
  content: "\ee99";
}
.icon-calendar1:before {
  content: "\ee9a";
}
.icon-call-add1:before {
  content: "\ee9b";
}
.icon-call-calling1:before {
  content: "\ee9c";
}
.icon-call-incoming1:before {
  content: "\ee9d";
}
.icon-call-minus1:before {
  content: "\ee9e";
}
.icon-call-outgoing1:before {
  content: "\ee9f";
}
.icon-call-received1:before {
  content: "\eea0";
}
.icon-call-remove1:before {
  content: "\eea1";
}
.icon-call-slash1:before {
  content: "\eea2";
}
.icon-call1:before {
  content: "\eea3";
}
.icon-camera-slash1:before {
  content: "\eea4";
}
.icon-camera1:before {
  content: "\eea5";
}
.icon-candle-21:before {
  content: "\eea6";
}
.icon-candle1:before {
  content: "\eea7";
}
.icon-car1:before {
  content: "\eea8";
}
.icon-card-add1:before {
  content: "\eea9";
}
.icon-card-coin1:before {
  content: "\eeaa";
}
.icon-card-edit1:before {
  content: "\eeab";
}
.icon-card-pos1:before {
  content: "\eeac";
}
.icon-card-receive1:before {
  content: "\eead";
}
.icon-card-remove-11:before {
  content: "\eeae";
}
.icon-card-remove1:before {
  content: "\eeaf";
}
.icon-card-send1:before {
  content: "\eeb0";
}
.icon-card-slash1:before {
  content: "\eeb1";
}
.icon-card-tick-11:before {
  content: "\eeb2";
}
.icon-card-tick1:before {
  content: "\eeb3";
}
.icon-card1:before {
  content: "\eeb4";
}
.icon-cards1:before {
  content: "\eeb5";
}
.icon-category-21:before {
  content: "\eeb6";
}
.icon-category1:before {
  content: "\eeb7";
}
.icon-cd1:before {
  content: "\eeb8";
}
.icon-chart-11:before {
  content: "\eeb9";
}
.icon-chart-22:before {
  content: "\eeba";
}
.icon-chart-31:before {
  content: "\eebb";
}
.icon-chart-211:before {
  content: "\eebc";
}
.icon-chart-square1:before {
  content: "\eebd";
}
.icon-chart-success1:before {
  content: "\eebe";
}
.icon-chart1:before {
  content: "\eebf";
}
.icon-check1:before {
  content: "\eec0";
}
.icon-chrome1:before {
  content: "\eec1";
}
.icon-clipboard-close1:before {
  content: "\eec2";
}
.icon-clipboard-export1:before {
  content: "\eec3";
}
.icon-clipboard-import1:before {
  content: "\eec4";
}
.icon-clipboard-text1:before {
  content: "\eec5";
}
.icon-clipboard-tick1:before {
  content: "\eec6";
}
.icon-clipboard1:before {
  content: "\eec7";
}
.icon-clock-11:before {
  content: "\eec8";
}
.icon-clock1:before {
  content: "\eec9";
}
.icon-close-circle1:before {
  content: "\eeca";
}
.icon-close-square1:before {
  content: "\eecb";
}
.icon-cloud-add1:before {
  content: "\eecc";
}
.icon-cloud-change1:before {
  content: "\eecd";
}
.icon-cloud-connection1:before {
  content: "\eece";
}
.icon-cloud-cross1:before {
  content: "\eecf";
}
.icon-cloud-drizzle1:before {
  content: "\eed0";
}
.icon-cloud-fog1:before {
  content: "\eed1";
}
.icon-cloud-lightning1:before {
  content: "\eed2";
}
.icon-cloud-minus1:before {
  content: "\eed3";
}
.icon-cloud-notif1:before {
  content: "\eed4";
}
.icon-cloud-plus1:before {
  content: "\eed5";
}
.icon-cloud-remove1:before {
  content: "\eed6";
}
.icon-cloud-snow1:before {
  content: "\eed7";
}
.icon-cloud-sunny1:before {
  content: "\eed8";
}
.icon-cloud1:before {
  content: "\eed9";
}
.icon-code-11:before {
  content: "\eeda";
}
.icon-code-circle1:before {
  content: "\eedb";
}
.icon-code1:before {
  content: "\eedc";
}
.icon-coffee1:before {
  content: "\eedd";
}
.icon-coin-11:before {
  content: "\eede";
}
.icon-coin1:before {
  content: "\eedf";
}
.icon-color-swatch1:before {
  content: "\eee0";
}
.icon-colorfilter1:before {
  content: "\eee1";
}
.icon-colors-square-1:before {
  content: "\eee2";
}
.icon-colors-square1:before {
  content: "\eee3";
}
.icon-command-square1:before {
  content: "\eee4";
}
.icon-command1:before {
  content: "\eee5";
}
.icon-component-1:before {
  content: "\eee6";
}
.icon-component1:before {
  content: "\eee7";
}
.icon-computing1:before {
  content: "\eee8";
}
.icon-convert-3d-cube1:before {
  content: "\eee9";
}
.icon-convert1:before {
  content: "\eeea";
}
.icon-convertshape-21:before {
  content: "\eeeb";
}
.icon-convertshape1:before {
  content: "\eeec";
}
.icon-copy-success1:before {
  content: "\eeed";
}
.icon-copy1:before {
  content: "\eeee";
}
.icon-copyright1:before {
  content: "\eeef";
}
.icon-courthouse1:before {
  content: "\eef0";
}
.icon-cpu-charge1:before {
  content: "\eef1";
}
.icon-cpu-setting1:before {
  content: "\eef2";
}
.icon-cpu1:before {
  content: "\eef3";
}
.icon-creative-commons1:before {
  content: "\eef4";
}
.icon-crop1:before {
  content: "\eef5";
}
.icon-crown-11:before {
  content: "\eef6";
}
.icon-crown1:before {
  content: "\eef7";
}
.icon-cup1:before {
  content: "\eef8";
}
.icon-danger1:before {
  content: "\eef9";
}
.icon-data-21:before {
  content: "\eefa";
}
.icon-data1:before {
  content: "\eefb";
}
.icon-designtools1:before {
  content: "\eefc";
}
.icon-device-message1:before {
  content: "\eefd";
}
.icon-devices-11:before {
  content: "\eefe";
}
.icon-devices1:before {
  content: "\eeff";
}
.icon-diagram1:before {
  content: "\ef00";
}
.icon-diamonds1:before {
  content: "\ef01";
}
.icon-direct-down1:before {
  content: "\ef02";
}
.icon-direct-inbox1:before {
  content: "\ef03";
}
.icon-direct-left1:before {
  content: "\ef04";
}
.icon-direct-normal1:before {
  content: "\ef05";
}
.icon-direct-notification1:before {
  content: "\ef06";
}
.icon-direct-right1:before {
  content: "\ef07";
}
.icon-direct-send1:before {
  content: "\ef08";
}
.icon-direct-up1:before {
  content: "\ef09";
}
.icon-direct1:before {
  content: "\ef0a";
}
.icon-directbox-default1:before {
  content: "\ef0b";
}
.icon-directbox-notif1:before {
  content: "\ef0c";
}
.icon-directbox-receive1:before {
  content: "\ef0d";
}
.icon-directbox-send1:before {
  content: "\ef0e";
}
.icon-discount-circle1:before {
  content: "\ef0f";
}
.icon-discount-shape1:before {
  content: "\ef10";
}
.icon-discover-11:before {
  content: "\ef11";
}
.icon-discover1:before {
  content: "\ef12";
}
.icon-dislike1:before {
  content: "\ef13";
}
.icon-document-11:before {
  content: "\ef14";
}
.icon-document1:before {
  content: "\ef15";
}
.icon-dollar-circle1:before {
  content: "\ef16";
}
.icon-element-41:before {
  content: "\ef17";
}
.icon-element-equal1:before {
  content: "\ef18";
}
.icon-element-plus1:before {
  content: "\ef19";
}
.icon-emoji-happy1:before {
  content: "\ef1a";
}
.icon-emoji-normal1:before {
  content: "\ef1b";
}
.icon-export-31:before {
  content: "\ef1c";
}
.icon-export1:before {
  content: "\ef1d";
}
.icon-external-drive1:before {
  content: "\ef1e";
}
.icon-eye-slash1:before {
  content: "\ef1f";
}
.icon-eye1:before {
  content: "\ef20";
}
.icon-firstline1:before {
  content: "\ef21";
}
.icon-flag-21:before {
  content: "\ef22";
}
.icon-flag1:before {
  content: "\ef23";
}
.icon-flash-11:before {
  content: "\ef24";
}
.icon-flash-circle-11:before {
  content: "\ef25";
}
.icon-folder1:before {
  content: "\ef26";
}
.icon-format-circle1:before {
  content: "\ef27";
}
.icon-format-square1:before {
  content: "\ef28";
}
.icon-forward-5-seconds1:before {
  content: "\ef29";
}
.icon-forward-10-seconds1:before {
  content: "\ef2a";
}
.icon-forward-15-seconds1:before {
  content: "\ef2b";
}
.icon-forward-item-1:before {
  content: "\ef2c";
}
.icon-forward-item1:before {
  content: "\ef2d";
}
.icon-forward-square1:before {
  content: "\ef2e";
}
.icon-forward1:before {
  content: "\ef2f";
}
.icon-frame-11:before {
  content: "\ef30";
}
.icon-frame-21:before {
  content: "\ef31";
}
.icon-frame-31:before {
  content: "\ef32";
}
.icon-frame-41:before {
  content: "\ef33";
}
.icon-frame-5:before {
  content: "\ef34";
}
.icon-frame1:before {
  content: "\ef35";
}
.icon-gallery-add1:before {
  content: "\ef36";
}
.icon-gallery-edit1:before {
  content: "\ef37";
}
.icon-gallery-export1:before {
  content: "\ef38";
}
.icon-gallery-favorite1:before {
  content: "\ef39";
}
.icon-gallery-import1:before {
  content: "\ef3a";
}
.icon-gallery-remove1:before {
  content: "\ef3b";
}
.icon-gallery-slash1:before {
  content: "\ef3c";
}
.icon-gallery-tick1:before {
  content: "\ef3d";
}
.icon-gallery1:before {
  content: "\ef3e";
}
.icon-game1:before {
  content: "\ef3f";
}
.icon-gameboy1:before {
  content: "\ef40";
}
.icon-gas-station1:before {
  content: "\ef41";
}
.icon-gemini-21:before {
  content: "\ef42";
}
.icon-gemini1:before {
  content: "\ef43";
}
.icon-ghost1:before {
  content: "\ef44";
}
.icon-gift1:before {
  content: "\ef45";
}
.icon-glass-11:before {
  content: "\ef46";
}
.icon-glass1:before {
  content: "\ef47";
}
.icon-global-edit1:before {
  content: "\ef48";
}
.icon-global-refresh1:before {
  content: "\ef49";
}
.icon-global-search1:before {
  content: "\ef4a";
}
.icon-global1:before {
  content: "\ef4b";
}
.icon-gps-slash1:before {
  content: "\ef4c";
}
.icon-gps1:before {
  content: "\ef4d";
}
.icon-grammerly1:before {
  content: "\ef4e";
}
.icon-graph1:before {
  content: "\ef4f";
}
.icon-grid-11:before {
  content: "\ef50";
}
.icon-grid-21:before {
  content: "\ef51";
}
.icon-grid-31:before {
  content: "\ef52";
}
.icon-grid-41:before {
  content: "\ef53";
}
.icon-d-cube-scan1:before {
  content: "\ef54";
}
.icon-d-rotate1:before {
  content: "\ef55";
}
.icon-d-square1:before {
  content: "\ef56";
}
.icon-dcube1:before {
  content: "\ef57";
}
.icon-square1:before {
  content: "\ef58";
}
.icon-support1:before {
  content: "\ef59";
}
.icon-activity1:before {
  content: "\ef5a";
}
.icon-add-circle1:before {
  content: "\ef5b";
}
.icon-add-square1:before {
  content: "\ef5c";
}
.icon-add1:before {
  content: "\ef5d";
}
.icon-additem1:before {
  content: "\ef5e";
}
.icon-airdrop1:before {
  content: "\ef5f";
}
.icon-airplane-square1:before {
  content: "\ef60";
}
.icon-airplane1:before {
  content: "\ef61";
}
.icon-airpod1:before {
  content: "\ef62";
}
.icon-airpods1:before {
  content: "\ef63";
}
.icon-alarm1:before {
  content: "\ef64";
}
.icon-align-bottom1:before {
  content: "\ef65";
}
.icon-align-horizontally1:before {
  content: "\ef66";
}
.icon-align-left1:before {
  content: "\ef67";
}
.icon-align-right1:before {
  content: "\ef68";
}
.icon-align-vertically1:before {
  content: "\ef69";
}
.icon-aquarius1:before {
  content: "\ef6a";
}
.icon-archive-11:before {
  content: "\ef6b";
}
.icon-archive-add1:before {
  content: "\ef6c";
}
.icon-archive-book1:before {
  content: "\ef6d";
}
.icon-archive-minus1:before {
  content: "\ef6e";
}
.icon-archive-slash1:before {
  content: "\ef6f";
}
.icon-archive-tick1:before {
  content: "\ef70";
}
.icon-archive1:before {
  content: "\ef71";
}
.icon-arrange-circle-21:before {
  content: "\ef72";
}
.icon-arrange-circle1:before {
  content: "\ef73";
}
.icon-arrange-square-21:before {
  content: "\ef74";
}
.icon-arrange-square1:before {
  content: "\ef75";
}
.icon-arrow-21:before {
  content: "\ef76";
}
.icon-arrow-31:before {
  content: "\ef77";
}
.icon-arrow-bottom1:before {
  content: "\ef78";
}
.icon-arrow-circle-down1:before {
  content: "\ef79";
}
.icon-arrow-circle-left1:before {
  content: "\ef7a";
}
.icon-arrow-circle-right1:before {
  content: "\ef7b";
}
.icon-arrow-down-11:before {
  content: "\ef7c";
}
.icon-arrow-down-21:before {
  content: "\ef7d";
}
.icon-arrow-down1:before {
  content: "\ef7e";
}
.icon-arrow-left-11:before {
  content: "\ef7f";
}
.icon-arrow-left-21:before {
  content: "\ef80";
}
.icon-arrow-left-31:before {
  content: "\ef81";
}
.icon-arrow-left1:before {
  content: "\ef82";
}
.icon-arrow-right-11:before {
  content: "\ef83";
}
.icon-arrow-right-21:before {
  content: "\ef84";
}
.icon-arrow-right-31:before {
  content: "\ef85";
}
.icon-arrow-right1:before {
  content: "\ef86";
}
.icon-arrow-square-down1:before {
  content: "\ef87";
}
.icon-arrow-square-left1:before {
  content: "\ef88";
}
.icon-arrow-square-right1:before {
  content: "\ef89";
}
.icon-arrow-square-up1:before {
  content: "\ef8a";
}
.icon-arrow-square1:before {
  content: "\ef8b";
}
.icon-arrow-swap-horizontal1:before {
  content: "\ef8c";
}
.icon-arrow-up-11:before {
  content: "\ef8d";
}
.icon-arrow-up-21:before {
  content: "\ef8e";
}
.icon-arrow-up-31:before {
  content: "\ef8f";
}
.icon-arrow-up1:before {
  content: "\ef90";
}
.icon-arrow1:before {
  content: "\ef91";
}
.icon-attach-circle1:before {
  content: "\ef92";
}
.icon-attach-square1:before {
  content: "\ef93";
}
.icon-audio-square1:before {
  content: "\ef94";
}
.icon-autobrightness1:before {
  content: "\ef95";
}
.icon-award1:before {
  content: "\ef96";
}
.icon-back-square1:before {
  content: "\ef97";
}
.icon-backward-5-seconds1:before {
  content: "\ef98";
}
.icon-backward-10-seconds1:before {
  content: "\ef99";
}
.icon-backward-15-seconds1:before {
  content: "\ef9a";
}
.icon-backward-item-1:before {
  content: "\ef9b";
}
.icon-backward-item1:before {
  content: "\ef9c";
}
.icon-backward1:before {
  content: "\ef9d";
}
.icon-bag-21:before {
  content: "\ef9e";
}
.icon-bag-cross-11:before {
  content: "\ef9f";
}
.icon-bag-cross1:before {
  content: "\efa0";
}
.icon-bag-happy1:before {
  content: "\efa1";
}
.icon-bag-tick-21:before {
  content: "\efa2";
}
.icon-bag-tick1:before {
  content: "\efa3";
}
.icon-bag-timer1:before {
  content: "\efa4";
}
.icon-bag1:before {
  content: "\efa5";
}
.icon-bank1:before {
  content: "\efa6";
}
.icon-barcode1:before {
  content: "\efa7";
}
.icon-battery-charging1:before {
  content: "\efa8";
}
.icon-battery-disable1:before {
  content: "\efa9";
}
.icon-battery-empty-11:before {
  content: "\efaa";
}
.icon-battery-empty1:before {
  content: "\efab";
}
.icon-battery-full1:before {
  content: "\efac";
}
.icon-bezier-1:before {
  content: "\efad";
}
.icon-bezier1:before {
  content: "\efae";
}
.icon-bill1:before {
  content: "\efaf";
}
.icon-bitcoin-card1:before {
  content: "\efb0";
}
.icon-bitcoin-convert1:before {
  content: "\efb1";
}
.icon-bitcoin-refresh1:before {
  content: "\efb2";
}
.icon-blend-21:before {
  content: "\efb3";
}
.icon-blend1:before {
  content: "\efb4";
}
.icon-bluetooth-21:before {
  content: "\efb5";
}
.icon-bluetooth-circle1:before {
  content: "\efb6";
}
.icon-bluetooth-rectangle1:before {
  content: "\efb7";
}
.icon-bluetooth1:before {
  content: "\efb8";
}
.icon-blur-1:before {
  content: "\efb9";
}
.icon-blur1:before {
  content: "\efba";
}
.icon-book-11:before {
  content: "\efbb";
}
.icon-book-saved1:before {
  content: "\efbc";
}
.icon-book-square1:before {
  content: "\efbd";
}
.icon-book1:before {
  content: "\efbe";
}
.icon-bookmark-21:before {
  content: "\efbf";
}
.icon-bookmark1:before {
  content: "\efc0";
}
.icon-box-11:before {
  content: "\efc1";
}
.icon-box-21:before {
  content: "\efc2";
}
.icon-box-add1:before {
  content: "\efc3";
}
.icon-box-remove1:before {
  content: "\efc4";
}
.icon-box-search1:before {
  content: "\efc5";
}
.icon-box-tick1:before {
  content: "\efc6";
}
.icon-box-time1:before {
  content: "\efc7";
}
.icon-box1:before {
  content: "\efc8";
}
.icon-briefcase1:before {
  content: "\efc9";
}
.icon-brifecase-cross1:before {
  content: "\efca";
}
.icon-brifecase-tick1:before {
  content: "\efcb";
}
.icon-brifecase-timer1:before {
  content: "\efcc";
}
.icon-broom1:before {
  content: "\efcd";
}
.icon-brush-11:before {
  content: "\efce";
}
.icon-brush-21:before {
  content: "\efcf";
}
.icon-brush-31:before {
  content: "\efd0";
}
.icon-brush1:before {
  content: "\efd1";
}
.icon-bubble1:before {
  content: "\efd2";
}
.icon-bucket-circle-1:before {
  content: "\efd3";
}
.icon-bucket-circle1:before {
  content: "\efd4";
}
.icon-bucket-square-1:before {
  content: "\efd5";
}
.icon-bucket-square1:before {
  content: "\efd6";
}
.icon-building-31:before {
  content: "\efd7";
}
.icon-building-41:before {
  content: "\efd8";
}
.icon-building1:before {
  content: "\efd9";
}
.icon-buildings-21:before {
  content: "\efda";
}
.icon-buildings1:before {
  content: "\efdb";
}
.icon-buliding1:before {
  content: "\efdc";
}
.icon-document-cloud1:before {
  content: "\efdd";
}
.icon-document-code-21:before {
  content: "\efde";
}
.icon-document-code1:before {
  content: "\efdf";
}
.icon-document-copy1:before {
  content: "\efe0";
}
.icon-document-download1:before {
  content: "\efe1";
}
.icon-document-favorite1:before {
  content: "\efe2";
}
.icon-document-filter1:before {
  content: "\efe3";
}
.icon-document-forward1:before {
  content: "\efe4";
}
.icon-document-like1:before {
  content: "\efe5";
}
.icon-document-normal1:before {
  content: "\efe6";
}
.icon-document-previous1:before {
  content: "\efe7";
}
.icon-document-sketch1:before {
  content: "\efe8";
}
.icon-document-text-11:before {
  content: "\efe9";
}
.icon-document-text1:before {
  content: "\efea";
}
.icon-document-upload1:before {
  content: "\efeb";
}
.icon-dollar-square1:before {
  content: "\efec";
}
.icon-driver-21:before {
  content: "\efed";
}
.icon-driver-refresh1:before {
  content: "\efee";
}
.icon-driver1:before {
  content: "\efef";
}
.icon-driving1:before {
  content: "\eff0";
}
.icon-drop1:before {
  content: "\eff1";
}
.icon-edit-21:before {
  content: "\eff2";
}
.icon-edit1:before {
  content: "\eff3";
}
.icon-electricity1:before {
  content: "\eff4";
}
.icon-element-1:before {
  content: "\eff5";
}
.icon-element-21:before {
  content: "\eff6";
}
.icon-element-31:before {
  content: "\eff7";
}
.icon-emoji-sad1:before {
  content: "\eff8";
}
.icon-empty-wallet-add1:before {
  content: "\eff9";
}
.icon-empty-wallet-change1:before {
  content: "\effa";
}
.icon-empty-wallet-remove1:before {
  content: "\effb";
}
.icon-empty-wallet-tick1:before {
  content: "\effc";
}
.icon-empty-wallet-time1:before {
  content: "\effd";
}
.icon-empty-wallet1:before {
  content: "\effe";
}
.icon-eraser-11:before {
  content: "\efff";
}
.icon-eraser-2:before {
  content: "\f000";
}
.icon-eraser1:before {
  content: "\f001";
}
.icon-export-11:before {
  content: "\f002";
}
.icon-export-21:before {
  content: "\f003";
}
.icon-fatrows1:before {
  content: "\f004";
}
.icon-favorite-chart1:before {
  content: "\f005";
}
.icon-filter-add1:before {
  content: "\f006";
}
.icon-filter-edit1:before {
  content: "\f007";
}
.icon-filter-remove1:before {
  content: "\f008";
}
.icon-filter-search1:before {
  content: "\f009";
}
.icon-filter-square1:before {
  content: "\f00a";
}
.icon-filter-tick1:before {
  content: "\f00b";
}
.icon-filter1:before {
  content: "\f00c";
}
.icon-finger-cricle1:before {
  content: "\f00d";
}
.icon-finger-scan1:before {
  content: "\f00e";
}
.icon-flash-circle-2:before {
  content: "\f00f";
}
.icon-flash-circle1:before {
  content: "\f010";
}
.icon-flash-slash1:before {
  content: "\f011";
}
.icon-flash1:before {
  content: "\f012";
}
.icon-folder-21:before {
  content: "\f013";
}
.icon-folder-add1:before {
  content: "\f014";
}
.icon-folder-cloud1:before {
  content: "\f015";
}
.icon-folder-connection1:before {
  content: "\f016";
}
.icon-folder-cross1:before {
  content: "\f017";
}
.icon-folder-favorite1:before {
  content: "\f018";
}
.icon-folder-minus1:before {
  content: "\f019";
}
.icon-folder-open1:before {
  content: "\f01a";
}

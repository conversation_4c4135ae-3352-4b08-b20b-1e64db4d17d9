/*-----------------------------------------------------------------------------------

    CSS INDEX
    ===================

01. Global 
02. header
03. hero
04. Service
05. Brand
06. About Us
07. Video Banner
08. Marqueue
09. Property Details
10. Newsletter˙
11. FAQ
12. Features
13. Countdown
14. Experiences
15. testimonial
16. Team Member
17. Footer
18. <PERSON><PERSON><PERSON> 
19. Side bar 

--------------------------------------------------------------------------------*/


@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

/*----------------------------------------------------
/* 01. Global */
/*----------------------------------------------------*/
:root {
  --cb-color-black: #0f0f0f;
  --cb-color-black2: #1A1A1A;
  --cb-color-brown: #EBD163;
  --cb-color-gray-100: #f7f4f1;
  --cb-color-gray-400: #2C2C2C;
  --cb-color-gray-300: #898989;
  --cb-color-gray-500: #CCCCCC;
  --cb-color-white: #F5F5F5;
  --cb-color-gray-200: #EBEBEB;
  --cb-color-white: #ffffff;
  --cb-color-white-opacity: #ffffff;
  --cb-color-black-opacity: #00000022;
  --transition-base: all 0.3s;
}
.form-control:focus,
button:visited,
button.active,
button:hover,
button:focus,
input:visited,
input.active,
input:hover,
input:focus,
textarea:hover,
textarea:focus,
a:hover,
a:focus,
a:visited,
a.active,
select,
select:hover,
select:focus,
select:visited {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  text-decoration: none;
  /* color: initial; */
}
iframe{
  width: 560px;
  height: 315px;
}
li{
  list-style-type: none;
}
button{
  background: transparent;
  border: none;
}
.contaner-1290{
  max-width: 1290px;
}
img{
  width: 100%;
}
.input:focus{
  border-color: initial;
  box-shadow: inherit;
}
body{
  background-color: var(--cb-color-gray-200);
  position: relative;
  z-index: -5;
}
.cb-font-body {
  font-family: "Roboto", sans-serif;
}
.cb-bg-color-black {
  background-color: var(--cb-color-black);
}
.cb-bg-color-gray-100 {
  background-color: var(--cb-color-gray-100);
}
.cb-bg-color-brown {
  background-color: var(--cb-color-brown);
}
.cb-bgcolor-gray-300 {
  background-color: var(--cb-color-gray-300);
}
.cb-color-gray300 {
  color: var(--cb-color-gray-300);
}
.cb-bg-color-white {
  background-color: var(--cb-color-white);
}
.cb-bg-color-gray-400 {
  background-color: var(--cb-color-gray-400);
}
.cb-bg-color-gray-500 {
  background-color: var(--cb-color-gray-500);
}
.cb-color-gray-500 {
  color: var(--cb-color-gray-500);
}
.cb-color-white {
  color: var(--cb-color-white);
}
.cb-color-black {
  color: #0f0f0f!important;
}
.cb-color-black2{
  color: var( --cb-color-black2);
}
.cb-color-brown {
  color: #EBD163;
}
.cb-color-gray-100 {
  color: #f7f4f1;
}
.cb-color-gray-200 {
  color: #e0e0e0;
}
.cb-color
.cb-color-gray-700 {
  color: #515151;
}
.cb-color-gray-500 {
  color: var(--cb-color-gray-500);
}
.cb-color-gray-800 {
  color: #777;
}
.cb-color-white-opacity {
  color: var(--cb-color-white-opacity);
  opacity: 0.7;
}
.opa-90{
  opacity: 90%;
}
.cb-clr-black-opa {
  border-color: var(--cb-color-black-opacity);
}
.cb-ff{
  font-family: "Roboto","sans-serif";
}
.cb-br-10{
  border-radius: 10px;
}
.cb-br-20{
  border-radius: 20px;
}
.cb-br-50{
  border-radius: 50px;
}
.cb-fs-12 {
  font-size: 12px;
}
.cb-fs-14 {
  font-size: 14px;
}
.cb-fs-12 {
    font-size: 12px;
  }
.cb-fs-16 {
  font-size: 16px;
}
.cb-fs-18 {
  font-size: 18px;
}
.cb-fs-20 {
  font-size: 20px;
}
.cb-fs-22 {
  font-size: 22px;
}
.cb-fs-24 {
  font-size: 24px;
}
.cb-fs-28 {
  font-size: 28px;
}
.cb-fs-32 {
  font-size: 32px;
}
.cb-fs-36 {
  font-size: 36px;
}
.cb-fs-40 {
  font-size: 40px;
}
.cb-fs-48 {
  font-size: 48px;
}
.cb-fs-60 {
  font-size: 60px;
}
.cb-fs-65 {
  font-size: 65px;
}
.cb-fs-70 {
  font-size: 70px;
}
.cb-fs-93 {
  font-size: 93px;
}
.cb-fs-162 {
  font-size: 162px;
}
.cb-fw-300 {
  font-weight:300;
}
.cb-fw-400 {
  font-weight: 400;
}
.cb-fw-500 {
  font-weight: 500;
}
.cb-fw-600 {
  font-weight: 600;
}
.cb-fw-700 {
  font-weight: 700;
}
.cb-fw-900 {
  font-weight: 900;
}
.cb-lh-19 {
  line-height: 19px;
}
.cb-lh-16 {
  line-height: 16px;
}
.cb-lh-24 {
  line-height: 24px;
}
.cb-lh-25 {
  line-height: 25px;
}
.cb-lh-27 {
  line-height:27px;
}
.cb-lh-30 {
  line-height: 30px;
}
.cb-lh-33 {
  line-height: 33.72px;
}
.cb-lh-36 {
  line-height: 36px;
}
.cb-lh-40 {
  line-height: 40px;
}
.cb-lh-43 {
  line-height: 43.2px;
}
.cb-lh-44 {
  line-height: 44.929px;
}
.cb-lh-50 {
  line-height: 50.427px;
}
.cb-lh-62 {
  line-height: 62px;
}
.cb-lh-70 {
  line-height: 70px;
}
.cb-lh-82 {
  line-height: 82.5px;
}
.cb-lh-86 {
  line-height: 86.674px;
}
.cb-lh-114 {
  line-height: 114px;
}
.cb-ls-1 {
  letter-spacing: 1.54px;
}
.cb-ls-07 {
  letter-spacing: -0.76px;
}
.cb-border-radius-5 {
  border-radius: 5px;
}
.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}
.g-30,
.gx-30 {
  --bs-gutter-x: 30px;
}
.g-45,
.gx-45 {
  --bs-gutter-x: 45px;
}
.g-65,
.gx-65 {
  --bs-gutter-x: 65px;
}
.g-30,
.gy-30 {
  --bs-gutter-y: 30px;
}
.tran5s{
  transition: .5s;
}
.cb-width-45 {
  width: 45%;
}
.cb-width-50 {
  width: 50%;
}
.cb-gap-20 {
  gap: 20px;
}
.cb-gap-30 {
  gap: 30px;
}
.cb-gap-45 {
  gap: 45px;
}
.odometer.odometer-auto-theme, .odometer.odometer-theme-default{
  font-family: var(--font-heading);
}
.tran3{
  transition: all ease .3s;
}
.ps-rel{
  position: relative;
}
.ps-abs{
  position: absolute;
}
.z-index1{
  z-index: 1;
}
.z-index-1{
  z-index: -1;
}
.z-index-5{
  z-index: -5;
}
.btn{
  border: none;
}
@-webkit-keyframes scale_fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 60px, 0) rotate(0deg);
    transform: translate3d(0, 60px, 0) rotate(00deg);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0) rotate(0deg);
    transform: translate3d(0, 0, 0) rotate(0deg);
  }
}

@keyframes scale_fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 60px, 0) rotate(-10deg);
    transform: translate3d(0, 60px, 0) rotate(0deg);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0) rotate(0deg);
    transform: translate3d(0, 0, 0) rotate(0deg);
  }
}

@keyframes waves {
  0% {
    -webkit-transform: scale(0.2, 0.2);
    transform: scale(0.2, 0.2);
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  }
  50% {
    opacity: 0.9;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)";
  }
  100% {
    -webkit-transform: scale(0.9, 0.9);
    transform: scale(0.9, 0.9);
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  }
}

@keyframes spin {
  100% {
    transform: rotateZ(360deg);
  }
}

@keyframes anim {
  0%,
  100% {
    width: 100%;
    height: 100%;
  }
  50% {
    width: 50%;
    height: 50%;
  }
}

@keyframes anims2 {
  100% {
    width: 100%;
  }
}

@keyframes newanimation {
  0% {
    background-position: 0;
  }
  60% {
    background-position: 180px;
  }
  100% {
    background-position: 180px;
  }
}

@keyframes toRightFromLeft {
  49% {
    transform: translate(100%);
  }
  50% {
    opacity: 0;
    transform: translate(-100%);
  }
  51% {
    opacity: 1;
  }
}

@keyframes fadeeffect {
  0% {
    opacity: 0;
    -webkit-transform: translateY(40px);
    -ms-transform: translateY(40px);
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes iconanimation {
  49% {
    transform: translateY(-100%);
  }
  50% {
    opacity: 0;
    transform: translateY(100%);
  }
  51% {
    opacity: 1;
  }
}
@keyframes bouncing {
  from,
  to {
    transform: scale(1, 1);
  }
  25% {
    transform: scale(0.9, 1.1);
  }
  50% {
    transform: scale(1.1, 0.9);
  }
  75% {
    transform: scale(0.95, 1.05);
  }
}
/* Animation Part End */

/* Global Hover Effect Start */

.nb_btn_hover_all:hover:before {
  top: -30px;
  left: -87px;
  width: 807px;
}
.nb_btn_hover_all:before {
  content: "";
  position: absolute;
  z-index: -1;
  background: var(--main-color);
  height: 150px;
  width: 200px;
  border-radius: 50%;
}
.nb_btn_hover_all {
  display: icbine-block;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: color 0.5s;
  z-index: 0;
}
.nb_btn_hover_all:hover:before {
  top: -30px;
  left: -87px;
  width: 807px;
}
.cb-button svg,
.cb-button i {
  display: icbine-block;
}

.cb-button-hover:hover .cb-button i,
.cb-button-hover:hover .cb-button svg ,
.cb-button:hover img {
  -webkit-animation: iconanimation 0.5s forwards;
  -moz-animation: iconanimation 0.5s forwards;
  animation: iconanimation 0.5s forwards;
}

.cb-blog-icon:before {
  content: attr(data-letters);
  position: absolute;
  z-index: 2;
  overflow: hidden;
  color: var(--cb-color-brown);
  white-space: nowrap;
  width: 0%;
  transition: width 0.4s 0.3s;
}
.cb-blog-icon:hover::before {
  width: 100%;
}
.cb-blog-icon:hover {
  color: var(--cb-color-brown);
}
.cb-blog-icon:hover i {
  -webkit-animation: toRightFromLeft 0.5s forwards;
  -khtml-animation: toRightFromLeft 0.5s forwards;
  -moz-animation: toRightFromLeft 0.5s forwards;
  -ms-animation: toRightFromLeft 0.5s forwards;
  -o-animation: toRightFromLeft 0.5s forwards;
  animation: toRightFromLeft 0.5s forwards;
}
/* Global Hover Effect End */

body {
  margin: 0;
  padding: 0;
  z-index: 1;
  font-size: 18px;
  font-weight: 400;
  line-height: 30px;
  overflow-x: hidden;
  color: var(--cb-color-gray-800);
  font-family: var(--font-body);
}

.container-1290 {
  max-width: 1290px;
}
.container-1722 {
  max-width: 1746px;
}

.g-30,
.gx-30 {
  --bs-gutter-x: 30px;
}

.g-30,
.gy-30 {
  --bs-gutter-y: 30px;
}

ul {
  margin: 0;
  padding: 0;
}

[data-background] {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

a {
  color: inherit;
  text-decoration: none;
  transition: all 0.3s;
}
img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
}


.form-control {
  -webkit-box-shadow: none;
  box-shadow: none;
}

p {
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-family: var(--font-heading);
  font-weight: 400;
}

.ul-li ul li {
  list-style: none;
}
.center{
  display: grid;
  place-items: center;
}
.tran3s{
  transition: .3s;
}
/*-- Margin Top --*/
.cb-mt-5{margin-top:5px}.mt-10{margin-top:10px}.mt-15{margin-top:15px}.mt-20{margin-top:20px}.mt-25{margin-top:25px}.mt-30{margin-top:30px}.mt-35{margin-top:35px}.mt-40{margin-top:40px}.mt-45{margin-top:45px}.mt-50{margin-top:50px}.mt-55{margin-top:55px}.mt-60{margin-top:60px}.mt-65{margin-top:65px}.mt-70{margin-top:70px}.mt-75{margin-top:75px}.mt-80{margin-top:80px}.mt-85{margin-top:85px}.mt-90{margin-top:90px}.mt-95{margin-top:95px}.mt-100{margin-top:100px}.mt-105{margin-top:105px}.mt-110{margin-top:110px}.mt-115{margin-top:115px}.mt-120{margin-top:120px}.mt-125{margin-top:125px}.mt-130{margin-top:130px}.mt-135{margin-top:135px}.mt-140{margin-top:140px}.mt-145{margin-top:145px}.mt-150{margin-top:150px}.mt-155{margin-top:155px}.mt-160{margin-top:160px}.mt-165{margin-top:165px}.mt-170{margin-top:170px}.mt-175{margin-top:175px}.mt-180{margin-top:180px}.mt-185{margin-top:185px}.mt-190{margin-top:190px}.mt-195{margin-top:195px}.mt-200{margin-top:200px}.cb-mb-5{margin-bottom:5px}.mb-10{margin-bottom:10px}.mb-15{margin-bottom:15px}.mb-20{margin-bottom:20px}.mb-25{margin-bottom:25px}.mb-30{margin-bottom:30px}.mb-35{margin-bottom:35px}.mb-40{margin-bottom:40px}.mb-45{margin-bottom:45px}.mb-50{margin-bottom:50px}.mb-55{margin-bottom:55px}.mb-60{margin-bottom:60px}.mb-65{margin-bottom:65px}.mb-70{margin-bottom:70px}.mb-75{margin-bottom:75px}.mb-80{margin-bottom:80px}.mb-85{margin-bottom:85px}.mb-90{margin-bottom:90px}.mb-95{margin-bottom:95px}.mb-100{margin-bottom:100px}.mb-105{margin-bottom:105px}.mb-110{margin-bottom:110px}.mb-115{margin-bottom:115px}.mb-120{margin-bottom:120px}.mb-125{margin-bottom:125px}.mb-130{margin-bottom:130px}.mb-135{margin-bottom:135px}.mb-140{margin-bottom:140px}.mb-145{margin-bottom:145px}.mb-150{margin-bottom:150px}.mb-155{margin-bottom:155px}.mb-160{margin-bottom:160px}.mb-165{margin-bottom:165px}.mb-170{margin-bottom:170px}.mb-175{margin-bottom:175px}.mb-180{margin-bottom:180px}.mb-185{margin-bottom:185px}.mb-190{margin-bottom:190px}.mb-195{margin-bottom:195px}.mb-200{margin-bottom:200px}.ml-5{margin-left:5px}.ml-4{margin-left:4px}.ml-10{margin-left:10px}.ml-15{margin-left:15px}.ml-20{margin-left:20px}.ml-25{margin-left:25px}.ml-30{margin-left:30px}.ml-35{margin-left:35px}.ml-40{margin-left:40px}.ml-45{margin-left:45px}.ml-50{margin-left:50px}.ml-55{margin-left:55px}.ml-60{margin-left:60px}.ml-65{margin-left:65px}.ml-70{margin-left:70px}.ml-75{margin-left:75px}.ml-80{margin-left:80px}.ml-85{margin-left:85px}.ml-90{margin-left:90px}.ml-95{margin-left:95px}.ml-100{margin-left:100px}.mr-5{margin-right:5px}.mr-10{margin-right:10px}.mr-15{margin-right:15px}.mr-20{margin-right:20px}.mr-25{margin-right:25px}.mr-30{margin-right:30px}.mr-35{margin-right:35px}.mr-40{margin-right:40px}.mr-45{margin-right:45px}.mr-50{margin-right:50px}.mr-55{margin-right:55px}.mr-60{margin-right:60px}.mr-65{margin-right:65px}.mr-70{margin-right:70px}.mr-75{margin-right:75px}.mr-80{margin-right:80px}.mr-85{margin-right:85px}.mr-90{margin-right:90px}.mr-95{margin-right:95px}.mr-100{margin-right:100px}
/*-- Padding Top --*/
.cb-section-pt,.pt-120{padding-top:120px}.cb-pt-5{padding-top:5px}.pt-10{padding-top:10px}.pt-15{padding-top:15px}.pt-20{padding-top:20px}.pt-25{padding-top:25px}.pt-30{padding-top:30px}.pt-35{padding-top:35px}.pt-40{padding-top:40px}.pt-45{padding-top:45px}.pt-50{padding-top:50px}.pt-55{padding-top:55px}.pt-60{padding-top:60px}.pt-65{padding-top:65px}.pt-70{padding-top:70px}.pt-75{padding-top:75px}.pt-80{padding-top:80px}.pt-85{padding-top:85px}.pt-90{padding-top:90px}.pt-95{padding-top:95px}.pt-100{padding-top:100px}.pt-105{padding-top:105px}.pt-110{padding-top:110px}.pt-115{padding-top:115px}.pt-125{padding-top:125px}.pt-130{padding-top:130px}.pt-135{padding-top:135px}.pt-140{padding-top:140px}.pt-145{padding-top:145px}.pt-150{padding-top:150px}.pt-155{padding-top:155px}.pt-160{padding-top:160px}.pt-165{padding-top:165px}.pt-170{padding-top:170px}.pt-175{padding-top:175px}.pt-180{padding-top:180px}.pt-185{padding-top:185px}.pt-190{padding-top:190px}.pt-195{padding-top:195px}.pt-200{padding-top:200px}.cb-section-pb,.pb-120{padding-bottom:120px}.cb-pb-5{padding-bottom:5px}.pb-10{padding-bottom:10px}.pb-15{padding-bottom:15px}.pb-20{padding-bottom:20px}.pb-25{padding-bottom:25px}.pb-30{padding-bottom:30px}.pb-35{padding-bottom:35px}.pb-40{padding-bottom:40px}.pb-45{padding-bottom:45px}.pb-50{padding-bottom:50px}.pb-55{padding-bottom:55px}.pb-60{padding-bottom:60px}.pb-65{padding-bottom:65px}.pb-70{padding-bottom:70px}.pb-75{padding-bottom:75px}.pb-80{padding-bottom:80px}.pb-85{padding-bottom:85px}.pb-90{padding-bottom:90px}.pb-95{padding-bottom:95px}.pb-100{padding-bottom:100px}.pb-105{padding-bottom:105px}.pb-110{padding-bottom:110px}.pb-115{padding-bottom:115px}.pb-125{padding-bottom:125px}.pb-130{padding-bottom:130px}.pb-135{padding-bottom:135px}.pb-140{padding-bottom:140px}.pb-145{padding-bottom:145px}.pb-150{padding-bottom:150px}.pb-155{padding-bottom:155px}.pb-160{padding-bottom:160px}.pb-165{padding-bottom:165px}.pb-170{padding-bottom:170px}.pb-175{padding-bottom:175px}.pb-180{padding-bottom:180px}.pb-185{padding-bottom:185px}.pb-190{padding-bottom:190px}.pb-195{padding-bottom:195px}.pb-200{padding-bottom:200px}.pl-5{padding-left:5px}.pl-10{padding-left:10px}.pl-15{padding-left:15px}.pl-20{padding-left:20px}.pl-25{padding-left:25px}.pl-30{padding-left:30px}.pl-35{padding-left:35px}.pl-40{padding-left:40px}.pl-45{padding-left:45px}.pl-50{padding-left:50px}.pl-55{padding-left:55px}.pl-60{padding-left:60px}.pl-65{padding-left:65px}.pl-70{padding-left:70px}.pl-75{padding-left:75px}.pl-80{padding-left:80px}.pl-85{padding-left:85px}.pl-90{padding-left:90px}.pl-95{padding-left:95px}.pl-100{padding-left:100px}.pr-5{padding-right:5px}.pr-10{padding-right:10px}.pr-15{padding-right:15px}.pr-20{padding-right:20px}.pr-25{padding-right:25px}.pr-30{padding-right:30px}.pr-35{padding-right:35px}.pr-40{padding-right:40px}.pr-45{padding-right:45px}.pr-50{padding-right:50px}.pr-55{padding-right:55px}.pr-60{padding-right:60px}.pr-65{padding-right:65px}.pr-70{padding-right:70px}.pr-75{padding-right:75px}.pr-80{padding-right:80px}.pr-85{padding-right:85px}.pr-90{padding-right:90px}.pr-95{padding-right:95px}.pr-100{padding-right:100px}
/*----------------------------------------*/
/*  02. header
/*----------------------------------------*/

/* main header start */
.cb__main-header {
  padding: 16px 20px;
  width: 100%;
}
.hm1-bg{
  min-height: 90vh;
  background-image: linear-gradient(rgba(0,0,0,0.75),rgba(0,0,0,0.75)),url(../images/backg.png);
  border-radius: 0 0 10px 10px;
  background-position: center;
  background-size: cover;
}
.hdr-icon:hover{
  transform: scale(1.1);
}
.cb-navmenu {
  text-align: end;
}

.cb-navmenu > ul {
  display: icbine-block;
}

.cb-navmenu > ul > li > a {
  position: relative;
}

.cb-navmenu > ul > li > a::before {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: var(--cb-color-black3);
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
}
.cb__header-social-item a{
  border: 1px solid white;
  width: 30px;
  height: 30px;
  transition: all .3s;
}
.cb__header-social-item a:hover{
  background-color: var(--cb-color-brown);
  border: none;
}
.cmn-social-header a {
  border: none;
  width: 0;
  height: 0;
  display: contents;
}
.cb-navmenu > ul > li > a:hover::before {
  width: calc(100% - 32px);
}

.cb-navmenu > ul li {
  display: icbine-block;
  position: relative;
}

.cb-navmenu > ul li a {
  display: block;
  padding: 0 22px;
  color: var(--cb-color-black);
  font-family: "Roboto","sans-serif";
  font-size: 20px;
  font-weight: 400;
  line-height: 22px;
}
.hm2-navmenu > ul li a {
  color: var(--cb-color-white);
}
.hm2-navmenu > ul li.has-submenu::after {
  color: var(--cb-color-white)!important;
}
.cb-navmenu > ul li.has-submenu {
  position: relative;
}

.cb-navmenu > ul li.has-submenu::after {
  content: "\E00C";
  position: absolute;
  font-family: "Iconly";
  right: -2px;
  top: 2px;
  font-size: 13px;
  color: var(--cb-color-black);
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
  font-weight: 900;
  line-height: 10px;
}

.cb-navmenu > ul li.has-submenu > .submenu-wrapper {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  min-width: 220px;
  background-color: var(--cb-color-brown);
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
  -webkit-box-shadow: var(--box-shadow);
  box-shadow: var(--box-shadow);
  text-align: left;
}
.cb-navmenu > ul li.has-submenu > .submenu-wrapper li {
  display: block;
}

.cb-navmenu > ul li.has-submenu > .submenu-wrapper li a {
  color: var(--cb-color-black);
  padding: 10px 24px;
  position: relative;
}

.cb-navmenu > ul li.has-submenu > .submenu-wrapper li a::before {
  content: "";
  position: absolute;
  left: 12px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 0;
  height: 2px;
  background-color: var(--cb-color-brown);
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
}

.cb-navmenu > ul li.has-submenu > .submenu-wrapper li:hover > a {
  padding-left: 36px;
  color: var(--cb-color-black);
}

.cb-navmenu > ul li.has-submenu > .submenu-wrapper li:hover > a::before {
  width: 15px;
}

.cb-navmenu > ul li.has-submenu > .submenu-wrapper li.has-submenu::after {
  content: "\f105";
  top: 10px;
  right: 12px;
  color: var(--cb-color-black);
}

.cb-navmenu
  > ul
  li.has-submenu
  > .submenu-wrapper
  li.has-submenu
  > .submenu-wrapper {
  content: "\f105";
  position: absolute;
  left: 100%;
  top: 10px;
}

.cb-navmenu
  > ul
  li.has-submenu
  > .submenu-wrapper
  li.has-submenu:hover
  > .submenu-wrapper {
  top: 0;
}

.cb-navmenu > ul li.has-submenu > .submenu-wrapper li.has-submenu:hover::after {
  color: var(--cb-color-black);
}

.cb-navmenu > ul li.has-submenu:hover > .submenu-wrapper {
  top: 58px;
  opacity: 1;
  visibility: visible;
}

.cb-navmenu > ul li:hover > a {
  color: var(--cb-color-brown);
}

.cb-navmenu > ul li:hover::after {
  color: var(--cb-color-brown);
}
.cb-navmenu-v3 > ul li > a{
  color: var(--cb-color-white);
}
.cb-navmenu-v3 > ul li:hover > a {
  color: var(--cb-color-white);
}

.cb-navmenu-v3 > ul li:hover::after {
  color: var(--cb-color-white);
}
.cb-navmenu-v3 > ul li.has-submenu::after{
  color: var(--cb-color-white);
}
/* main-header-end */
/* feature-area-start */
.fea-bg-img{
  top: 0;
  right: -10%;
  animation: scle 5s linear infinite alternate;
}
.fea-star-img{
  bottom: -15%;
  left: -15%;
  animation: scle 2s linear infinite alternate;
}
@keyframes scle{
  100%{
    transform: scale(0);
  }
  100%{
    transform: scale(1.2);
  }
}
/* feature-area-end */
/* latest-gallery-start */
.counter-item{
  border: 1px solid gray;
  /* max-width: 273px; */
  height: 152px;
  flex-direction: column;
}
.counter-item h2 {
  background-image: linear-gradient(180deg, #000000 13%, #EDF8EB 100%);
  -webkit-background-clip: text;
  /* -webkit-text-fill-color: transparent; */
}
.latest-h2{
  max-width: 500px;
}
/* latest-gallery-end */
/* choose-us-part-start */
.choose-customer-review-part {
    width: 275px;
    height: 117px;
    left: 3%;
    bottom: 3%;
    z-index: 55;
}
.choose-img{
  width: 560px;
}
.choose-us-content h2{
  width: 630px;
}
.choose-img-border {
    width: 445px;
    height: 540px;
    border: 1px solid gray;
    right: -1%;
    top: -6%;
}
.bill-btn{
  width: 198px;
  height: 60px;
  border: 1px solid gray;
  Padding: 20px 26px 20px 26px;
}
.choose-btn:hover{
  background-color: #5f4e03;
  color: #F5F5F5;
}
/* choose-us-part-end */
/* map-part-start */
.map-form input{
  width: 100%;
  border: 1px solid gray;
  padding: 20px;
}
.map-from-btn {
    width: 60px;
    height: 60px;
    right: 1%;
    top: 7%;
    cursor: pointer;
}
/* #name{
  background-color: gray;
  width: fit-content;
  text-align: center;
  border-radius: 5px;
  border: 1px solid #ffff;
  color: #ffff;
  padding: 20px;
  opacity: 0;
} */
#time{
  background-color: rgb(206, 130, 130);
  width: fit-content;
  text-align: center;
  border-radius: 5px;
  border: 1px solid #ffff;
  color: #ffff;
  padding: 20px;
}
/* map-part-end */
/* billbord-part-start */
.billbord-img:hover .billbord-advertising{
  bottom: 0;
}
.billbord-img{
  overflow: hidden;
}
.billbord-img img {
  height: 520px;
}
.billbord-img:hover img{
	transform: scale(1.1) translateY(0px);
}
.billbord-advertising {
  width: 95%;
  bottom: -80%;
  left: 50%;
  transform: translate(-50%,-50%);
}
/* billbord-part-end */
/* banner-company-start */
.banner-company img{
  flex-basis: 10%;
}
/* banner-company-end */
/* video-banner-part-start */
.video-banner{
  background-image: url(../images/hm1-video-bnr.png);
  height: 60vh; 
  background-repeat: no-repeat;
  background-size: cover;
}
.video-banner-play-icon{
  width: 88px;
  height: 88px;
  transition: .3s;
}
.video-banner-play-icon:hover{
  background-color: #ecd983;
}
.video-banner-play-icon::after{
  position: absolute;
  content: "";
  width: 0;
  height: 120px;
  background-color: #b8a657;
  border-radius: 50%;
  z-index: -1;
  transition: .3s;
}
.video-banner-play-icon:hover::after{
  width: 140px;
  height: 140px;
}
/* video-banner-part-end */
/* latest-blog-part-start */
.card-item {
    border: 1px solid #e1dede;
}
.card-btn{
  width: 171px;
  height: 60px;
  border-radius: 999px;
}
.card-btn:hover>a{
  color: #ffff!important;
}
.card-btn:hover a button{
  color: #ffff!important;
}
.card-btn:hover a i{
  color: #ffff!important;
}
.card-btn a{
  z-index: 5;
}
.card-btn:hover >a button{
  color: white;
}
.card-btn:hover >a i{
  color: white;
}
/* latest-blog-part-end */
/* footer-part-start */
footer{
  padding: 30px 0;
  border-radius: 50px 50px 0 0;
}
.footer-img{
  width: 180px;
}
.about-item li{
  list-style-type: none;
}
.about-item li a:hover{
  transform: scale(1.1);
  color: var(--cb-color-brown);
  display: block;
}
.footer-social a{
  width: 35px;
  height: 35px;
}
.footer-social a:hover{
  transform: scale(1.2);
  background-color: var(--cb-color-brown);
}
/* footer-form *******************/
.footer-form-part{
  border: 2px solid var(--cb-color-brown);
  padding: 20px;
  transform: translateY(-100px);
}
.form-input input, textarea{
  width: 100%;
  background: transparent;
  border: none;
  border: 1px solid var(--cb-color-gray-400);
  padding: 10px 30px;
}
.hm1-footer-btn{
  border-radius: 68px;
  height: 60px;
  cursor: pointer;
}
.hm1-footer-btn:hover a button{
  color: #ffff;
}
/* footer-part-end */

/* contact-part-start *****************************************************/
.common-hero-area {
  min-height: 40vh;
  background-image: url(../images/bnrboard.png);
  background-size: cover;
  background-position: center;
}
.header-btn{
  width: 173px;
  height: 60px;
}
/* contact-part-start ************/
.contact-form input,.contact-form select {
  width: 100%;
  background-color: transparent;
  outline: none;
  height: 67px;
  padding: 0 15px;
}
.contact-form textarea{
  background-color: transparent;
}
.con-inp{
  border: 1px solid #cccccc;
}
.con-textarea{
  border: 1px solid #cccccc;
  height: 130px;
}
.contact-btn {
  width:200px;
  height: 60px;
  padding: 20px 30px 20px 30px;
  border-radius: 999px;
}
/* adderes-part *****/
.adderes-part{
  padding: 35px 60px;
}
.adderes-logo{
  width: 65px;
  height: 65px;
}
/* contact-part-end ************/
/* pricing-part-start *****************************************************/
.pricing-billboard h2{
  width: 630px;
  margin: 0 auto;
}
.pricing-billboard-package-img{
  overflow: hidden;
}
.pricing-billboard-package-img:hover{
  border-radius: 20px;
}
.pricing-billboard-package-img img:hover{
  transform: scale(1.2);
  cursor: pointer;
  border-radius: 20px;
}
.pricing-billboard-package-btn {
    border: 1px solid gray;
}
.pricing-billboard-package-btn:hover{
  background-color: var(--cb-color-brown);
  border: none;
  cursor: pointer;
}
/* pricing-part-end *****************************************************/
/* blog-part-start *****************************************************/
.blog-img img{
  height: 100%;
}
.blog-content{
  border: 1px solid var(--cb-color-gray-500);
  border-radius: 0 20px 20px 0;
  padding: 60px;
}
.blog-btn{
  border: 1px solid var(--cb-color-gray-500);
  width: 170px;
  height: 60px;
}
.blog-btn:hover button{
  color: white!important;
}
.page-item{
    width: 67px;
    height: 67px;
    border-radius: 50%;
    border: 1px solid var(--cb-color-gray-300);
}
.page-item:hover{
  background-color: var(--cb-color-brown);
}
.page-link{
  background: transparent;
  border: none;
}
.page-link:hover {
    z-index: 2;
    color: none;
    background-color: transparent;
    border-color: none;
}
.page-link:hover{
  outline: none;
}
/* blog-part-end *****************************************************/
/* Project-Details-part-start **************************/
.Project-Details-part h1{
  width: 705px;
}
.project-info{
  border-radius: 20px 0 20px 20px;
  transform: translateY(-100px);
}
.project-item{
  border: 1px solid var(--cb-color-gray-500);
  border-radius: 5px;
}
/* Project-Details-part-end **************************/
/* Project-part-start **************************/
.project-img img{
  height: auto;
}
/* Project-part-end **************************/
/* Service_Details-part-start **************************/
.serevice-lists{
  transform: translate(43px ,-100px);
  z-index: 55;
}
.serevice-get-touch{
  transform: translate(43px ,-40px);
}
.touch-icon{
  width: 35px;
  height: 35px;
}
.service-overview-billboard-img{
  width: 347px;
  margin: auto;
  transform: translateY(110px);
}
.service_details-billboard img:hover{
  filter:grayscale(60%);
}
.service-overview-billboard-img img{
  border-radius: 20px 20px 0 0;
}
.service-overview-billboard a {
  border: 1px solid var(--cb-color-gray-500);
  border-radius: 50px;
  padding: 20px 30px 20px 30px;
  transform: translatey(80px);
  display: inline-block;
  width: 171px;
  height: 60px;
}
/* Service_Details-part-end **************************/
/* Service-part-start **************************/
.features-img img{
  border-radius: 999px 999px 20px 20px;
}
/* Service_Details-part-end **************************/
/* About Us-part-start **************************/
.cmn-btn{
  width: 171px;
  height: 60px;
}
.about-chose-billboard-img{
  border-radius: 999px 999px 20px 20px;
}
.about-chose-billboard-img img{
  border-radius: 999px 999px 20px 20px;
}
.about-chose-clients {
  width: 212px;
  right: 6%;
  top: 25%;
  z-index: 55;
}
.round-img{
  left: 0;
  bottom: 0;
  z-index: -1;
  animation: rounded 5s linear infinite;
}
.hr-row{
  border-bottom: none;
  border: 1px solid #2C2C2C;
}
.about-btn{
  width: 189px;
  height: 60px;
  border: 1px solid #1a1a1a;
}
.about-video-banner {
    background-image: linear-gradient(89deg, rgba(0, 0, 0, 1) 30%, rgba(14, 14, 14, 0.86) 70%, rgba(102, 102, 102, 0)), url(../images/about/about-video-billboard.png);
    background-position: right;
    background-repeat: no-repeat;
    padding: 80px;
}
.sun-img{
  left: 0;
  top: 0;
  z-index: -1;
  animation: vibrat 5s linear infinite alternate;
}
@keyframes rounded{
  0%{
    transform: rotate(0);
  }
  100%{
    transform: rotate(360deg);
  }
}
@keyframes vibrat{
  0%{
    transform: rotate(0);
  }
  100%{
    transform: rotate(15deg);
  }
}
@keyframes zoom{
  0%{
    transform: scale(0);
  }
  100%{
    transform: scale(1);
  }
}
@keyframes zoom2{
  0%{
    transform: scale(0);
  }
  100%{
    transform: scale(1);
  }
}
/* get-touch-part-start **********/
.get-touch-part {
  padding: 40px 40px 130px 40px;
}
.get-contact-form input, textarea, select{
  border: none;
  border: 1px solid var(--cb-color-black2);
  background-color: transparent;
  width: 100%;
  padding: 15px 15px;
  overflow: hidden;
}
.get-contact-form input::placeholder,.get-contact-form textarea::placeholder{
  color: var(--cb-color-black2);
}
.pacthimg1{
  top: 0;
  right: 0;
  animation: zoom 5s linear infinite alternate;
}
.pacthimg2{
  left: 0;
  bottom: 0;
  animation: zoom2 5s linear infinite alternate;
  animation-delay: 2s;
}
.counter-items{
  border-right: 1px solid var(--cb-color-gray-500);
  padding-right: 50px;
}
.counter-items:last-child{
  border: none;
}
.counter-part{
  bottom: -35%;
  width: 100%;
}
/* get-touch-part-end **********/
/* testmonial-part-start **********/
.testmonial {
    border: 1px solid var(--cb-color-black2);
    padding-bottom: 100px!important;
}
.testmonial-img{
  width: 120px;
  height: 120px;
}
.testmonial-img img{
  border-radius: 50%;
}
.carousel-indicators {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: -70%;
}
.carousel-indicators [data-bs-target] {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #CCCCCC;
  background-clip: padding-box;
  border: 0;
  opacity: .5;
  transition: opacity .6s ease;
}
.carousel-indicators [data-bs-target]:active{
  background-color: rgb(0, 0, 0);
  transform: scale(1.2);
}
/* testmonial-part-end **********/
/* About Us-part-end **************************/
/* homepage2-start **************************/
.home2-hero-area{
  /* background-image: url(../placeholder/homepage2/homepage2.png);
  background-repeat: no-repeat;
  background-position: bottom right;
  background-size: auto; */
  padding-bottom: 130px;
  overflow: hidden;
}
.hero-para{
  width: 370px;
}
.hero-btn{
  width: 171px;
  height: 60px;
}
.need-help{
  bottom: 0;
  right: 0;
  z-index: 11;
}
.need-help-call{
  border-radius: 50px 0 0 0;
  padding: 45px;
}
.need-help-call-icon{
  width: 81px;
  height: 81px;
  border-radius: 50%;
  border: 1px solid var(--cb-color-gray-300);
}
.need-help-slide-icon{
  padding: 25px;
}
.btn-prev, .btn-next{
  border: 1px solid var(--cb-color-brown);
  border-radius: 50%;
  width: 45px;
  height: 45px;
  cursor: pointer;
  transition: .3s ease;
}
.btn-prev:hover, .btn-next:hover{
  border-color: #ffff;
}
.btn-prev:hover> i, .btn-next:hover> i{
  color: #ffff;
}
.hro {
  right: 0;
  bottom: 0;
  width: 60%;
}
/* .hero-img{
  width: 90%!important;
}
.hro img{
  width: 100%;
  display: block;
} */
 /* Features-part-start */
.Features-heading h1:last-child{
  width: 731px;
}
.latest-heading h1:last-child{
  width: 760px;
  letter-spacing: -1.06px;
}
.brand-map{
  padding: 120px 0;
}
.calender{
  top: 3%;
  left: 6%;
}
.hm1-calender {
  top: -21%;
  right: 3%;
}
.msg-btn{
  border-color: #1A1A1A!important;
}
.message-part{

}
 /* Features-part-end */
/* homepage2-end **************************/
/* homepage3-start **************************/
/* header-start *****************/
.hm3-header{
  background-image: url(../images/homepage3/heder3-bg1.png);
  background-position: center top;
  background-size: cover;
}
.header-bg{
  background-image: url(../placeholder/homepage3/heder3-bg1.png);
  background-repeat: no-repeat;
}
.choose-bg{
  background-image: url(../placeholder/homepage3/choose-bg.png);
  background-repeat: no-repeat;
  background-position: left;
}
.home-bg-3{
  background-image: url(../placeholder/homepage3/home3-bg3.png);
  background-repeat: no-repeat;
  background-position: right center;
}
.top-header-social a{
  width: 30px;
  height: 30px;
}
.top-header-social a:hover{
  background-color: var(--cb-color-brown);
  color: #0f0f0f;
}
.header_search input::placeholder{
  font-family: "Roboto","sans-serif";;
}
.searchtext {
  width: 0;
  outline: none;
  border: none;
  padding: 0!important;
  transition: .3s;
  background-color: #b3b3b3;
  margin-left: 20px;
}
.searchBtn{
  width: 3rem;
  height: 3rem;
  right: 0.3rem;
}
.header_search:hover > .searchtext{
  width: 15rem;
  padding: 0.5rem!important;
}
.hdr3icon{
  border-left: 1px solid #1A1A1A;
}
/* header-end *****************/
/* hero-area-start */
.hero-img3{
  transform: translateY(-140px);
}
.hompage3-img1{
  height: 218px;
}
.hompage3-img1 img{
  object-fit: contain;
}
.hompage3-img3{
  margin-top: -28px;
}
/* hero-area-end */
/* choose-us-start */
.choose-us-content-part h1{
  width: 520px;
}
.clients-part{
  width: 340px;
}
.clients-part::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(#DFFF5E ,#869938);
    left: -4px;
    z-index: -1;
    border-radius: 20px;
}
.clients-img img{
  width: 54px;
  height: 54px;
  margin-right: -17px;
  border-radius: 50%;
}
.clients-img img:hover{
  transform: scale(1.1);
  cursor: pointer;
}
/* service-part-start */
.cmn-heading h1{
  width: 760px;
  margin: 0 auto;
}
.service-billboard-img{
  width: 347px;
  margin: auto;
  transform: translateY(110px);
}
.service-billboard img,.features-img{
  border-radius: 50% 50% 20px 20px;
  transition: .3s;
  transition-delay: .1s;
}
.gallary-part{
  padding: 120px 25px;
}
/* service-part-end */
/* message-part-start */
.message-part {
  flex-basis: 750px;
  height: fit-content;
  transform: translate(10% ,7%);
  position: relative;
  z-index: 7;
}
.message-img-part{
  width: 715px;
  height: 839px;
}
.msg-btn:hover > a button{
  color: #ffff;
}
/* message-part-end */
 /* pricing-part-start */
.pricing-heading{
  width: 630px;
  margin: auto;
}
.pricing-billboard-package:hover>.pricing-billboard-package-img img{
  transform: scale(1.3)!important;
  filter:contrast(60%)
}
.pricing-billboard-package-btn:hover > a button{
  color: #ffff;
}
 /* pricing-part-end */
/* footer-start */
.footer-up {
  background-image: url(../images/homepage3/footer-img.png);
  border-radius: 999px;
  overflow: hidden;
  transform: translateY(-60%);
}
.top-footer-content{
  z-index: 5;
}
.footer-up::before{
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: #1A1A1A;
  opacity: 80%;
}
.top-footer-content p{
  width: 728px;
  margin: auto;
}
.top-footer-email{
  z-index: 5;
}
.top-footer-email input{
  width: 379px;
  height: 60px;
  border: none;
  padding-left: 15px;
}
.footer-btn{
  width: 164px;
  height: 60px;
}
.footer-logo{
  width: 180px;
}
.ftr-para{
  width: 341px;
}
.Newsletter-icon{
  width: 40px;
  height: 40px;
}
.footer-last-part{
  border-top: 1px solid var(--cb-color-gray-300);
}
.footer{
  z-index: 5;
}
.bulb {
    left: 0;
    bottom: 7%;
    animation: vibrat 5s linear infinite alternate;
}
.bulb img {
    opacity: 19%;
}
/* footer-end */
/* homepage3-end **************************/
/* common-footer-start */
.cmn-footer-header p{
  width: 735px;
  margin: auto;
}
.footer-email{
  width: 607px;
  height: 76px;
  margin: 20px auto;
}
.footer-email input{
  width: 100%;
  height: 76px;
  background: transparent;
  border: none;
  border: 1px solid #CCCCCC;
  padding-left: 40px;
}
.footer-subs-btn {
    width: 140px;
    height: 60px;
    right: 2%;
    top: 11%;
}
.footer-body {
  border-bottom: 1px solid gray;
}
.cmn-footer-logo img{
  width: 50%;
}
.cmn-footer-contact-part-icon{
  width: 40px;
  height: 40px;
}
.cmn-footer-social-part li{
  list-style-type: none;
  width: 35px;
  height: 35px;
}
.cmn-footer-social-part li a{
  line-height: 0;
}
.cmn-footer-social-part li:hover{
  background-color: var(--cb-color-brown);
  transform: scale(1.2);
}
.cmn-footer-item li{
  list-style-type: none;
}
.cmn-footer-item li a:hover{
  color: var(--cb-color-brown);
}
.footer-subs-btn:hover{
  background-color: #fff;
  border: 3px solid var(--cb-color-brown);
  transform: scale(1.1);
}
/* common-footer-end */
/************************ mobile menu start ****************/
.mobile-menu {
  width: 300px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: -310px;
  background-color: var(--cb-color-gray-100);
  z-index: 60;
  padding: 60px 24px;
  overflow-y: scroll;
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
}

.mobile-menu .close {
  position: absolute;
  right: 20px;
  top: 20px;
  color: var(--cb-color-black);
}

.mobile-menu .close:hover {
  color: var(--cb-color-black);
}

.mobile-menu .logo {
  width: 100%;
  display: block;
  background-color: var(--cb-color-white);
  text-align: center;
  padding: 30px;
  border-radius: 10px;
}

.mobile-menu .mobile-nav-menu {
  margin-top: 32px;
}

.mobile-menu .mobile-nav-menu li {
  display: block;
}

.mobile-menu .mobile-nav-menu li + li {
  border-top: 1px solid var(--light-stroke);
}

.mobile-menu .mobile-nav-menu li a {
  display: block;
  color: var(--cb-color-black);
  padding: 10px 0;
  font-family: Jost;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 26.272px; /* 164.2% */
}

.mobile-menu .mobile-nav-menu li a:hover {
  color: var(--cb-color-black);
}

.mobile-menu .mobile-nav-menu li.has-submenu {
  position: relative;
}

.mobile-menu .mobile-nav-menu li.has-submenu > i {
  position: absolute;
  right: 0;
  top: 6px;
  padding: 8px;
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
  font-weight: 900;
}

.mobile-menu .mobile-nav-menu li.has-submenu > i.icon-rotate {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.mobile-menu .mobile-nav-menu li.has-submenu > .submenu-wrapper {
  padding-left: 15px;
  display: none;
}

.mobile-menu .mobile-search input {
  width: 100%;
}

.mobile-menu .mobile-search button span {
  padding: 0;
  width: 50px;
  height: 50px;
}

.mobile-menu.active {
  left: 0;
}

/* mobile toggle button */
.header-toggle {
  min-width: 50px;
  min-height: 50px;
  border-radius: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 5px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-color: var(--cb-color-brown);
  border: none;
}
.header-toggle-btn {
  min-width: 50px;
  min-height: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 5px;
  position: relative;
  z-index: 1;
}
.header-toggle span {
  width: 20px;
  height: 2px;
  background-color: var(--cb-color-black);
  display: block;
  position: relative;
  z-index: 2;
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
}

.offcanvas_border_one {
  width: 10px !important;
  margin-right: -5px;
}

.header-toggle::before {
  content: "";
  position: absolute;
  left: -10px;
  top: -145%;
  width: 150%;
  height: 143%;
  background-color: var(--cb-color-black);
  z-index: -1;
  border-radius: 40%;
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
}

.header-toggle:hover {
  color: var(--white-color);
}

.header-toggle:hover span {
  background-color: var(--cb-color-white);
}

.header-toggle:hover::before {
  top: -6px;
}
.main-heading {
  text-align: center;
  font-family: roboto;
  font-family:'Raleway', sans-serif;    
  font-weight: 400;
  font-size: 35px;
}

.dummy-text {
  padding: 0 50px;
  text-align: justify;
  line-height: 1.5;
  font-size: 20px;
  font-family: roboto;
}
/*All Animations*/

.zoom-in {
  -webkit-transform: scale(0);
  transform: scale(0);

  -webkit-transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
}

.tileContainer{
  border:10px solid black;
  display:icbine-block;
  position:relative;
  width:1024px;
  height:768px;
  overflow:hidden;
  box-sizing:border-box;
}
.tile{
  position:relative;
  vertical-align:top;
  display:icbine-block;
  border-right:1px solid rgba(0, 0, 0, 0.5);
  border-bottom:1px solid rgba(0, 0, 0, 0.5);  
  box-sizing:border-box;
}

.tile:after{
  content:"";
  background-color:#cc1c32;
  width:6px;
  height:6px;
  position:absolute;
  top:100%;
  right:0px;
  transform:translate(50%, -50%);
  z-index:2;
  line-height:1;
}

/*-- no grid --*/

.noGrid .tile{
  border-right:0px solid rgba(0, 0, 0, 0.5);
  border-bottom:0px solid rgba(0, 0, 0, 0.5);
}

.noGrid .tile:after{
  content:none;
}
/* image-effect-white-black ********/
.img-effect{
  transition: .3s;
  transition-delay: .5s;
  overflow: hidden;
}
.img-effect:hover{
  filter: grayscale(100%);
}
.image-hover::before {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 1;
  display: block;
  content: '';
  width: 0;
  height: 0;
  background: rgb(185 151 85 / 38%);
 
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: All 0.3s;
}

.image-hover:hover img {
  filter: grayscale(100%);
  transform: scale(1.05,1.05) rotate(2deg);
  transition: 0.3s;
  transition-delay: .3s;
}
.image-hover {
  position: relative;
  overflow: hidden;
  transition-delay: .3s;
}
.image-hover img{
  transition: 0.5s;
}

.cb_testimonial{
  overflow: visible;
}
/****************** offcanvus start **************/
/* Circle */
.imghover figure {
  position: relative;
  overflow: hidden;
  height: 100%;
}
.imghover figure::before {
	position: absolute;
	top: 50%;
	left: 0;
	z-index: 2;
	display: block;
	content: "";
	width: 0;
	height: 0;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 100%;
	-webkit-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	opacity: 0;
}
.imghover figure:hover::before {
	-webkit-animation: circle 0.75s;
	animation: circle 0.95s;
}
@-webkit-keyframes circle {
	0% {
		opacity: 1;
	}
	40% {
		opacity: 1;
	}
	100% {
		width: 200%;
		height: 200%;
		opacity: 0;
	}
}
@keyframes circle {
	0% {
		opacity: 1;
	}
	40% {
		opacity: 1;
	}
	100% {
		width: 200%;
		height: 200%;
		opacity: 0;
	}
}
/* Flashing */
.imghover2 figure:hover img {
	opacity: 1;
	-webkit-animation: flash 3s;
	animation: flash 3s;
}
@-webkit-keyframes flash {
	0% {
		opacity: 0.4;
	}
	100% {
		opacity: 1;
	}
}
@keyframes flash {
	0% {
		opacity: 0.4;
	}
	100% {
		opacity: 1;
	}
}
/************* btn-effect1 *************/

.wrap *,
.wrap *:before,
.wrap *:after {
  box-sizing: border-box;
  transition: 0.5s ease-in-out;
}
.wrap i, .wrap em,
.wrap b, .wrap strong,
.wrap span {
  transition: none;
}

.btn-2:before,
.btn-2:after {
  z-index: -1;
}
a {
  text-decoration: none;
}

.centerer {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 0 1rem;
}
[class^=effect-] {
  position: relative;
  display: block;
  overflow: hidden;
  width: 100%;
  height: 80px;
  max-width: 250px;
  margin: 1rem auto;
  text-transform: uppercase;
  border: 1px solid currentColor;
}
.btn-2 {
  color: #979f50;
    text-align: center;
}
.btn-2:before, .btn-2:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.btn-2:before {
  right: -50px;
  border-right: 50px solid transparent;
  border-bottom: 80px solid #50551d;
  transform: translateX(-100%);
}
.btn-2:after {
  left: -50px;
  border-left: 50px solid transparent;
  border-top: 80px solid #50551d;
  transform: translateX(100%);
}
.btn-2:hover {
  color: #e1e3cc;
}
.btn-2:hover:before {
  transform: translateX(-49%);
}
.btn-2:hover:after {
  transform: translateX(49%);
}
/************ btn-effect2 ************/

.buttonfx {
  outline: none;
  background: transparent;
  border: none;
  letter-spacing: 0.0625em;
  padding: 8px 18px;
  position: relative;
  display: inline-block;
  cursor: pointer;
  overflow: hidden;
  transition: all .5s;
}

/* //// Default effect: Slide from Top  //// */

.buttonfx:before,
.buttonfx:after{
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  background: #012214; /* onhover background color */
  z-index: -1;
  transform: translate3D(0,-100%,0); /* move elements above button so they don't appear initially */
  transition: all .5s;
}

.buttonfx:before{
  background: var(--cb-color-brown); /* button default background color */
  z-index: -2;
  transform: translate3D(0,0,0);
}
.buttonfx a:hover{
	color: white;
}
.buttonfx :hover> i{
	color: white;
}
.buttonfx:hover:after{
  transform: translate3D(0,0,0);
  transition: all .5s;
}
/* //// Slide Corner  //// */

.slidebottomleft:after{
  transform: translate3D(-100%,100%,0);
}

.slidebottomleft:hover:after{
	transform: translate3D(0,0,0);
  transition: all .5s;
}
/************ btn-effect-2 ************/
.btn-effect {
  position: relative;
  transition: all 0.5s ease 0s;
  z-index: 1;
  border-radius: 999px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.btn-effect2{
  position: relative;
  transition: all 0.5s ease 0s;
  z-index: 1;
  border-radius: 999px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.btn-effect:hover {
  color: #34464f;
  border: 3px solid var(--cb-color-brown);
}
.btn-effect2:hover {
  color: #ffffff;
  background-color: transparent;
  border: none!important;
}
.btn-effect:hover a{
  text-decoration: none;
  transform: scale(1.1);
  letter-spacing: 1px;
  color: #000000;
}
.btn-effect2:hover a{
  text-decoration: none;
  transform: scale(1.1);
  letter-spacing: 1px;
}
.btn-effect:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0px;
  background: rgb(255, 255, 255);
  border-radius: 30px;
  transform: scaleX(0);
  transition: all 0.3s ease-out 0s;
  transform-origin: 50% 50% 0;
  z-index: -1;
}
.btn-effect2:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0px;
  background: transparent;
  border-radius: 30px;
  transform: scaleX(0);
  transition: all 0.3s ease-out 0s;
  transform-origin: 50% 50% 0;
  z-index: -1;
}
.btn-effect:hover:before {
  transform: scaleX(1);
}
.btn-effect2:hover:before {
  transform: scaleX(1);
  background-color: #300202;
}
.btn-effect span {
  position: absolute;
  display: block;
  height: 220px;
  width: 30px;
  transform: rotateZ(45deg);
  background: var(--cb-color-brown);
  margin-top: -180px;
  margin-left: -60px;
  z-index: -1;
}
.btn-effect2 span {
  position: absolute;
  display: block;
  height: 220px;
  width: 30px;
  transform: rotateZ(45deg);
  background: var(--cb-color-brown);
  margin-top: -180px;
  margin-left: -60px;
  z-index: -1;
}
.btn-effect:hover span{
  -webkit-animation: line 0.5s ease-out forwards;
  -webkit-animation-delay: 0.2s;
}
@-webkit-keyframes line {
  from {
    margin-top: -80px;
    margin-left: -50px;
  }
  to {
    margin-top: -80px;
    margin-left: 350px;
  }
}
.btn-effect2:hover span{
  -webkit-animation: line 0.5s ease-out forwards;
  -webkit-animation-delay: 0.2s;
}
@-webkit-keyframes line {
  from {
    margin-top: -80px;
    margin-left: -50px;
  }
  to {
    margin-top: -80px;
    margin-left: 350px;
  }
}
/* text-effect */
.txteff {
	color: rgba(66, 65, 65, 0.8);
	position: relative;
	display: block;
	text-decoration: none;
}

.text-eff a {
	color: #000000;
}

.text-eff a:before {
	color: #000000;
	content: attr(data-hover);
	position: absolute;
	-webkit-transition: -webkit-transform 0.3s, opacity 0.3s;
	-moz-transition: -moz-transform 0.3s, opacity 0.3s;
	transition: transform 0.3s, opacity 0.3s;
}

.text-eff a:hover:before, .text-eff a:focus:before {
	-webkit-transform: scale(1.2);
	-moz-transform: scale(1.2);
	transform: scale(1.2);
	opacity: 0;
}
/* circle button */
.button-circle-bottom {
    color: rgba(2, 2, 2, 0.75);
    border-radius: 50%;
    transition: 0.5s ease-in-out;
}
figure {
  margin: 0 0 0rem;
}
.button-circle-bottom:hover {
    color: white;
    box-shadow: 7px 7px 15px 6px rgba(55, 55, 55, 0.2), -7px -7px 15px #ddd, inset 0 -5rem 4px #ff9900, inset 0 0 0 transparent;
}


/* offcanvas button */



/****************** offcanvus start **************/
.offcanvus-box {
  width: 425px;
  z-index: 100;
  top: 0;
  right: -430px;
  height: 100vh;
  overflow-y: scroll;
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
}

.offcanvus-box.active {
  right: 0;
}

.offcanvus-box .offcanvus-close {
  position: absolute;
  left: 30px;
  top: 30px;
  color: var(--body-color);
}

.offcanvus-box .offcanvus-close:hover {
  color: var(--body-color);
}

.offcanvus-box .content-top {
  text-align: center;
  padding: 120px 60px 100px;
}

.offcanvus-box .content-top p {
  line-height: 26px;
}

.offcanvus-box .offcanvus-gallery {
  padding: 0 40px;
  gap: 10px;
}

.offcanvus-box .offcanvus-gallery a {
  width: calc(33% - 6px);
  overflow: hidden;
}

.offcanvus-box .offcanvus-gallery a img {
  max-width: 100%;
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
}

.offcanvus-box .offcanvus-gallery a img:hover {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}

.offcanvus-box .offcanvus-newsletter {
  padding: 100px 40px;
}

.offcanvus-box .offcanvus-newsletter h6 {
  font-size: 24px;
}
.offcanvas-email {
  width: 100%;
}
.offcanvus-box .offcanvus-newsletter input {
  -webkit-transition: var(--transition-base);
  -o-transition: var(--transition-base);
  transition: var(--transition-base);
  width: 100%;
  outline: none;
  border: 1px solid gray;
  padding: 12px 20px;
}
.offcanvus-form-btn{
  width: 30%;
  right: 4px;
  top: 4px;
  height: 45px;
}
.offcanvus-form-btn:hover{
  background-color: #fff;
  border: 2px solid var(--cb-color-brown);
}


/****************** Preloder start **************/

/* PRELOADER */
.preloader {
  width: 100vw;
  max-width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99;
  transition: all cubic-bezier(0.785, 0.135, 0.15, 0.86) 1s;
  overflow: hidden;
  background-color:#EBD163 ;
}
.preloader .inner {
  display: inline-block;
  position: relative;
  z-index: 1;
  animation-name: preloader-inner;
  animation-duration: 0.8s;
  transition: all ease 0.8s;
}
.preloader .svg {
  display: inline-block;
  position: relative;
  z-index: 1;
  left: auto;
    top: auto;
    width: 50%;
}
.preloader .inner figure {
  display: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  position: absolute;

}
.preloader .inner figure img {
  height: 70px;
}
.preloader .inner small {
  width: 100%;
  display: block;
  color: #fff;
  font-family: var(--heading-font);
  font-size: 24px;
  text-align: center;
  margin-top: 15px;
}
.preloader .inner .progress-bar {
  background: none;
  display: none;
}

.preloader svg path {
  animation-name: preloader-svg;
  -webkit-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -moz-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -o-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  -ms-animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  animation-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
  animation-duration: 0.7s;
  animation-play-state: paused;
  animation-fill-mode: forwards;
}
.preloader svg path{
  fill: #ffffff;
}
/* PAGE LOADED */
.page-loaded .preloader {
  top: 100%;
}

.page-loaded .preloader .inner {
  transform: translateY(50px);
  opacity: 0;
}

.page-loaded .preloader svg path {
  animation-play-state: running;
}

.page-loaded .navbar {
  transform: translateY(0);
}
.nb__hero-section,
.nbv2_hero{
  transform: scale(1.4);
  transition-duration: 0.7s;
  -webkit-transition-duration: 0.7s;
  transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.09, 1);
  transition-delay: 0.25s;
  opacity: 0;
}
.page-loaded .nb__hero-section ,
.page-loaded .nbv2_hero{
  transform: scale(1);
  opacity: 1;
}

.page-loaded .page-header {
  transform: scale(1);
  opacity: 1;
}




.pin {
  -webkit-animation-name: pin;
  animation-duration: 1.2s;
  animation-iteration-count: infinite; 
  width: 30px;
  height: 30px;
  border-radius: 50% 50% 50% 0;
  background: #312783;
  position: absolute;
  transform: rotate(-45deg);
  left: 50%;
  top: 50%;
  margin: -20px 0 0 -20px;
  z-index: 2;
}
.pin:after {
  content: '';
  width: 14px;
  height: 14px;
  margin: 8px 0 0 8px;
  background: #ffe388;
  position: absolute;
  border-radius: 50%;
}
.pulse {
  background: rgba(0,0,0,0.2);
  border-radius: 50%;
  height: 14px;
  width: 14px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: 11px 0px 0px -12px;
  transform: rotateX(55deg);
  z-index: 1;
}
.pulse:after {
  content: "";
  border-radius: 50%;
  height: 40px;
  width: 40px;
  position: absolute;
  margin: -13px 0 0 -13px;
  -webkit-animation: pulsate 1s ease-out;
  -webkit-animation-iteration-count: infinite;
  opacity: 0;
  box-shadow: 0 0 1px 2px #312783;
}
@-webkit-keyframes pulsate {
  0% {
    -webkit-transform: scale(0.1, 0.1);
    transform: scale(0.1, 0.1);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1.2, 1.2);
    transform: scale(1.2, 1.2);
  }
}
@-webkit-keyframes bounce {
  0% {
    -webkit-transform: translateY(-2000px) rotate(-45deg);

  }
  60% {
    -webkit-transform: translateY(30px) rotate(-45deg);
  }
  80% {
    -webkit-transform: translateY(-10px) rotate(-45deg);
  }
  100% {
    -webkit-transform: translateY(0) rotate(-45deg);
  }
}

@-webkit-keyframes pin{
    100% {top:45%; bottom:60px;}
}
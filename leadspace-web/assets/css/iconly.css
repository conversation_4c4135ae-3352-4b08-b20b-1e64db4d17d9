/*!
 *  Iconly icon font. Generated by Iconly: https://iconly.io/
 */

 @font-face {
  font-display: auto;
  font-family: "Iconly";
  font-style: normal;
  font-weight: 400;
  src: url("../fonts/iconly.ttf");
}

.nl-icon {
  display: inline-block;
  font-family: "Iconly" !important;
  font-weight: 400;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

.nl-icon-bathtub:before {
  content: "\e000";
}

.nl-icon-x-twitter:before {
  content: "\e001";
}

.nl-icon-twitter:before {
  content: "\e002";
}

.nl-icon-star:before {
  content: "\e003";
}

.nl-icon-star-2:before {
  content: "\e004";
}

.nl-icon-sqft:before {
  content: "\e005";
}

.nl-icon-sofa:before {
  content: "\e006";
}

.nl-icon-shield:before {
  content: "\e007";
}

.nl-icon-search:before {
  content: "\e008";
}

.nl-icon-quote:before {
  content: "\e009";
}

.nl-icon-play:before {
  content: "\e00a";
}

.nl-icon-pinterest:before {
  content: "\e00b";
}

.nl-icon-location-marker:before {
  content: "\e00c";
}

.nl-icon-linkedin:before {
  content: "\e00d";
}

.nl-icon-key:before {
  content: "\e00e";
}

.nl-icon-instagram:before {
  content: "\e00f";
}

.nl-icon-house-location:before {
  content: "\e010";
}

.nl-icon-house-location-2:before {
  content: "\e011";
}

.nl-icon-home:before {
  content: "\e012";
}

.nl-icon-home-3:before {
  content: "\e013";
}

.nl-icon-home-2:before {
  content: "\e014";
}

.nl-icon-heart:before {
  content: "\e015";
}

.nl-icon-heart-2:before {
  content: "\e016";
}

.nl-icon-facebook:before {
  content: "\e017";
}

.nl-icon-experience:before {
  content: "\e018";
}

.nl-icon-envelop:before {
  content: "\e019";
}

.nl-icon-design:before {
  content: "\e01a";
}

.nl-icon-circle-check:before {
  content: "\e01b";
}

.nl-icon-check:before {
  content: "\e01c";
}

.nl-icon-chat-group:before {
  content: "\e01d";
}

.nl-icon-call:before {
  content: "\e01e";
}

.nl-icon-bed:before {
  content: "\e01f";
}

.nl-icon-arrow-right:before {
  content: "\e020";
}

.nl-icon-arrow-long-right:before {
  content: "\e021";
}

.nl-icon-arrow-left:before {
  content: "\e022";
}

.nl-icon-arrow-double:before {
  content: "\e023";
}

.nl-icon-angle-down:before {
  content: "\e024";
}
.nl_footer_list_menu_icon li a{
border-bottom: 1px solid #515151;
}
.nl_footer_list_menu_icon li a:last-child{
 padding-bottom: 9px;
}
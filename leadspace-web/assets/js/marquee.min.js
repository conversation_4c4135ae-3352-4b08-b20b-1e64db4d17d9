/**
 * Skipped minification because the original files appears to be already minified.
 * Original file: /npm/node-marquee@3.0.6/build/cdn/index.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
/*
────────╔╗───     ───────────╔═╗─────────
╔═╦╗╔═╗╔╝║╔═╗     ╔══╗╔═╗─╔╦╗║╬║╔╦╗╔═╗╔═╗
║║║║║╬║║╬║║╩╣     ║║║║║╬╚╗║╔╝╚╗║║║║║╩╣║╩╣
╚╩═╝╚═╝╚═╝╚═╝     ╚╩╩╝╚══╝╚╝──╚╝╚═╝╚═╝╚═╝

GitHub Repository: https://github.com/antonbobrov/node-marquee

Powered by <PERSON> | https://github.com/antonbobrov
*/
(()=>{"use strict";function e(n,t){if(n instanceof Window)return n;if(function(e){return e instanceof HTMLElement||e instanceof Element}(n))return n;if(void 0!==t){const i=e(t);if(i)return i.querySelector(n)}return document.querySelector(n)}function n(e,n,t){const i=n.split(" ");for(let r=0;r<i.length;r++)void 0===t?e.classList.toggle(i[r]):t?e.classList.add(i[r]):e.classList.remove(i[r])}function t(e,t={}){const i=document.createElement(e);if(t.class&&function(e,t){if(e instanceof Element)n(e,t,!0);else for(let i=0;i<e.length;i++)n(e[i],t,!0)}(i,t.class),t.id&&i.setAttribute("id",t.id),t.attr)for(let n=0,r=t.attr.length;n<r;n++){const e=t.attr[n];i.setAttribute(e[0],e[1])}if(t.parent&&t.parent.appendChild(i),t.html&&(i.innerHTML=t.html),t.children)for(let n=0,r=t.children.length;n<r;n++)i.appendChild(t.children[n]);return i}const i=[];function r(e,n,t,r){if(void 0!==r){const i={passive:!1,once:!1};r.once&&(i.once=!0),r.passive&&(i.passive=!0),e.addEventListener(n,t,i)}else e.addEventListener(n,t);const a=`${Math.random()}-${+new Date}`;return i.push({id:a,el:e,target:n,callback:t}),{id:a,remove:o.bind(this,a)}}function o(e){const n=[];for(let t=0,r=i.length;t<r;t++){const r=i[t];r.id===e?r.el.removeEventListener(r.target,r.callback):n.push(r)}}function a(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=function(e,n){if(!e)return;if("string"==typeof e)return s(e,n);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return s(e,n)}(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){c=!0,o=e},f:function(){try{a||null==t.return||t.return()}finally{if(c)throw o}}}}function s(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,i=new Array(n);t<n;t++)i[t]=e[t];return i}window.nodeMarquee=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i={parent:"#node-marquee",speed:1,minQuantity:4,autoplay:!0,pauseOnHover:!1,useParentStyles:!0,prependWhitespace:!0,resize:!0},o=Object.assign(i,n),s="node-marquee",c=e(o.parent);if(!(c instanceof HTMLElement))return!1;c.classList.add(s);var u,l={width:window.innerWidth,height:window.innerHeight},f=!1,d=!1,h=0,p=!1,v=c.innerHTML,m=0,y=[],w=0,g=[r(window,"resize",M),r(c,"mouseenter",H),r(c,"mouseleave",A)];function b(){E(),m=0,y=[],c.innerHTML="",o.useParentStyles&&(c.style.position="relative",c.style.width="100%",c.style.overflow="hidden",c.style.whiteSpace="nowrap");var e=L();(w=e.clientWidth)<=0&&(w=window.innerWidth),w<c.clientWidth&&(m=Math.ceil((c.clientWidth+w)/w)),m<o.minQuantity&&(m=o.minQuantity);for(var n=1;n<m;n+=1)L(!0);q(),S(),setTimeout((function(){W()}),500)}function L(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=t("div",{class:"".concat(s,"__el"),html:"".concat(o.prependWhitespace?"&nbsp;":"").concat(v)});return e&&(n.style.position="absolute",n.style.top="0",n.style.left="0"),n.style.display="inline-block",c.appendChild(n),y.push(n),n}function M(){if(!1!==o.resize){var e=l.width,n=l.height,t=window.innerWidth,i=window.innerHeight;if(l.width=t,l.height=i,"string"==typeof o.resize)return"w"===o.resize&&e!==t||"h"===o.resize&&n!==i?void b():void 0;b()}}function H(){o.pauseOnHover&&C()}function A(){o.pauseOnHover&&j()}function S(){if(!u){(u=new MutationObserver((function(e){var n,t=a(e);try{for(t.s();!(n=t.n()).done;){"childList"===n.value.type&&(v=c.innerHTML,b())}}catch(i){t.e(i)}finally{t.f()}}))).observe(c,{childList:!0})}}function E(){u&&(u.disconnect(),u=void 0)}function W(){if(!f){for(var e=[],n=0;n<m;n+=1)e.push(y[n].clientWidth);w=Math.max.apply(Math,e)}}function T(){d&&(p=window.requestAnimationFrame(T)),q()}function q(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o.speed;h-=e;for(var n=w*(m-1),t=0;t<m;t+=1){var i=y[t],r=z(-w,n,h+w*t);i.style.transform="matrix3d(1,0,0.00,0,0.00,1,0.00,0,0,0,1,0, ".concat(r,", 0, 0,1)")}}function z(e,n,t){var i=n-e;return O(t,(function(n){return(i+(n-e)%i)%i+e}))}function O(e,n){return e||0===e?n(e):n}function j(){p||(d=!0,p=window.requestAnimationFrame(T))}function C(){d=!1,p&&(window.cancelAnimationFrame(p),p=!1)}function k(){f=!0,C(),E(),g.forEach((function(e){e.remove()})),c.innerHTML=v}return b(),o.autoplay&&j(),{play:j,pause:C,isPlaying:function(){return d},render:q,recreate:b,updateSizes:W,destroy:k}}})();